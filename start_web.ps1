#!/usr/bin/env powershell

Write-Host "🎮 PoE 2 Trade Valuation Tool - Web Interface" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Starting web server..." -ForegroundColor Green
Write-Host "The interface will open automatically in your browser" -ForegroundColor Yellow
Write-Host ""
Write-Host "🌐 URL: http://127.0.0.1:5000" -ForegroundColor Blue
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Red
Write-Host ""

& "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe" run_web.py

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
