#!/usr/bin/env python3
"""
Basic test script to verify the PoE 2 trade valuation tool setup.
"""

import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test that all modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test API module
        from poe_trade.api import TradeAPIClient, APIError
        print("✓ API module imported successfully")
        
        # Test models
        from poe_trade.models import Item, Modifier, SearchQuery, MarketData
        print("✓ Models module imported successfully")
        
        # Test config
        from poe_trade.config import Settings, load_config
        print("✓ Config module imported successfully")
        
        # Test CLI
        from poe_trade.cli import cli
        print("✓ CLI module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False


def test_config_creation():
    """Test configuration file creation."""
    print("\nTesting configuration...")
    
    try:
        from poe_trade.config.settings import create_example_config
        
        # Create a test config
        test_config_path = "test_config.yaml"
        create_example_config(test_config_path)
        
        # Check if file was created
        if Path(test_config_path).exists():
            print("✓ Example config created successfully")
            # Clean up
            Path(test_config_path).unlink()
            return True
        else:
            print("✗ Config file was not created")
            return False
            
    except Exception as e:
        print(f"✗ Config creation error: {e}")
        return False


def test_models():
    """Test data model creation."""
    print("\nTesting data models...")
    
    try:
        from poe_trade.models import Item, Modifier, ModifierType, ItemRarity
        
        # Create a test modifier
        modifier = Modifier(
            text="+10 to maximum Life",
            type=ModifierType.EXPLICIT,
            values=[10]
        )
        print(f"✓ Created modifier: {modifier.text}")
        
        # Create a test item
        item = Item(
            name="Test Item",
            type_line="Test Type",
            base_type="Test Base",
            rarity=ItemRarity.RARE,
            explicit_mods=[modifier]
        )
        print(f"✓ Created item: {item.name}")
        
        # Test modifier access
        all_mods = item.all_modifiers
        print(f"✓ Item has {len(all_mods)} modifiers")
        
        return True
        
    except Exception as e:
        print(f"✗ Model creation error: {e}")
        return False


def test_api_client_creation():
    """Test API client creation (without making requests)."""
    print("\nTesting API client creation...")
    
    try:
        from poe_trade.api import TradeAPIClient
        
        # Create client with dummy credentials
        client = TradeAPIClient(
            poesessid="dummy_session_id",
            rate_limit=60,
            timeout=30
        )
        print("✓ API client created successfully")
        
        # Test that rate limiter works
        can_make_request = client.rate_limiter.can_make_request()
        print(f"✓ Rate limiter working: {can_make_request}")
        
        return True
        
    except Exception as e:
        print(f"✗ API client creation error: {e}")
        return False


def main():
    """Run all tests."""
    print("PoE 2 Trade Valuation Tool - Basic Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config_creation,
        test_models,
        test_api_client_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("✓ All tests passed! The basic setup is working correctly.")
        print("\nNext steps:")
        print("1. Run 'python -m poe_trade.cli.main init-config' to create a config file")
        print("2. Edit config/config.yaml and set your POESESSID")
        print("3. Run 'python -m poe_trade.cli.main test-connection' to verify API access")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
