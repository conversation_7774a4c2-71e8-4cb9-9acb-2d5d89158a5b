"""
Data models for market data and pricing information.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from enum import Enum
from statistics import median, mean
from pydantic import BaseModel, Field, validator

from .items import Modifier
from .search import ItemListing


class Currency(str, Enum):
    """Supported currencies."""
    EXALTED = "exalted"
    DIVINE = "divine"
    CHAOS = "chaos"
    GOLD = "gold"


class PriceData(BaseModel):
    """Price information for an item or modifier."""
    
    currency: Currency
    amount: float
    confidence: float = Field(ge=0.0, le=1.0)  # 0-1 confidence score
    sample_size: int = 0
    last_updated: datetime = Field(default_factory=datetime.now)
    
    @validator('amount')
    def validate_amount(cls, v):
        """Ensure amount is positive."""
        if v < 0:
            raise ValueError("Price amount must be positive")
        return v
    
    def to_chaos_equivalent(self, exchange_rates: Dict[str, float]) -> float:
        """Convert price to chaos equivalent."""
        if self.currency == Currency.CHAOS:
            return self.amount
        
        rate = exchange_rates.get(self.currency.value, 1.0)
        return self.amount * rate
    
    def is_stale(self, max_age_hours: int = 24) -> bool:
        """Check if price data is stale."""
        age = datetime.now() - self.last_updated
        return age > timedelta(hours=max_age_hours)


class ModifierValue(BaseModel):
    """Value information for a specific modifier."""
    
    modifier_id: str
    modifier_text: str
    base_value: PriceData
    value_per_tier: Optional[PriceData] = None
    value_per_point: Optional[PriceData] = None  # For numeric modifiers
    
    # Statistical data
    min_price: Optional[PriceData] = None
    max_price: Optional[PriceData] = None
    median_price: Optional[PriceData] = None
    
    # Market data
    total_listings: int = 0
    recent_sales: int = 0
    trend: Optional[str] = None  # "rising", "falling", "stable"
    
    def get_value_for_tier(self, tier: int) -> Optional[PriceData]:
        """Get estimated value for a specific tier."""
        if self.value_per_tier and tier > 0:
            estimated_amount = self.base_value.amount + (self.value_per_tier.amount * (tier - 1))
            return PriceData(
                currency=self.base_value.currency,
                amount=estimated_amount,
                confidence=max(0.1, self.base_value.confidence - (tier * 0.1)),
                sample_size=self.base_value.sample_size
            )
        return self.base_value
    
    def get_value_for_roll(self, roll_value: Union[int, float], max_roll: Union[int, float]) -> Optional[PriceData]:
        """Get estimated value for a specific roll value."""
        if self.value_per_point and max_roll > 0:
            roll_percentage = roll_value / max_roll
            estimated_amount = self.base_value.amount + (self.value_per_point.amount * roll_value)
            confidence_modifier = 0.5 + (roll_percentage * 0.5)  # Higher rolls = higher confidence
            
            return PriceData(
                currency=self.base_value.currency,
                amount=estimated_amount,
                confidence=min(1.0, self.base_value.confidence * confidence_modifier),
                sample_size=self.base_value.sample_size
            )
        return self.base_value


class MarketData(BaseModel):
    """Market data for a specific item type."""
    
    item_type: str
    league: str
    base_price: Optional[PriceData] = None
    modifier_values: Dict[str, ModifierValue] = Field(default_factory=dict)
    
    # Market statistics
    total_listings: int = 0
    average_price: Optional[PriceData] = None
    price_distribution: List[PriceData] = Field(default_factory=list)
    
    # Metadata
    last_updated: datetime = Field(default_factory=datetime.now)
    data_quality: float = Field(default=0.0, ge=0.0, le=1.0)
    
    def add_modifier_value(self, modifier_value: ModifierValue):
        """Add or update a modifier value."""
        self.modifier_values[modifier_value.modifier_id] = modifier_value
    
    def get_modifier_value(self, modifier_id: str) -> Optional[ModifierValue]:
        """Get value for a specific modifier."""
        return self.modifier_values.get(modifier_id)
    
    def calculate_item_value(self, modifiers: List[Modifier]) -> Optional[PriceData]:
        """Calculate estimated value for an item with given modifiers."""
        if not self.base_price:
            return None
        
        total_value = self.base_price.amount
        total_confidence = self.base_price.confidence
        confidence_count = 1
        
        for modifier in modifiers:
            mod_value = self.get_modifier_value(modifier.id or modifier.text)
            if mod_value:
                if modifier.tier and mod_value.value_per_tier:
                    tier_value = mod_value.get_value_for_tier(modifier.tier)
                elif modifier.values and mod_value.value_per_point:
                    # Use the first value for simplicity
                    roll_value = modifier.values[0] if modifier.values else 0
                    # Estimate max roll (this would need to be configured)
                    max_roll = roll_value * 1.5  # Rough estimate
                    tier_value = mod_value.get_value_for_roll(roll_value, max_roll)
                else:
                    tier_value = mod_value.base_value
                
                if tier_value:
                    total_value += tier_value.amount
                    total_confidence += tier_value.confidence
                    confidence_count += 1
        
        # Average confidence across all components
        avg_confidence = total_confidence / confidence_count if confidence_count > 0 else 0.0
        
        return PriceData(
            currency=self.base_price.currency,
            amount=total_value,
            confidence=min(1.0, avg_confidence),
            sample_size=self.base_price.sample_size
        )
    
    def is_stale(self, max_age_hours: int = 24) -> bool:
        """Check if market data is stale."""
        age = datetime.now() - self.last_updated
        return age > timedelta(hours=max_age_hours)
    
    @classmethod
    def from_listings(cls, item_type: str, league: str, listings: List[ItemListing]) -> 'MarketData':
        """Create market data from a list of item listings."""
        if not listings:
            return cls(item_type=item_type, league=league)
        
        # Extract prices
        prices = []
        for listing in listings:
            if listing.price_value is not None:
                price_data = PriceData(
                    currency=Currency(listing.price_currency or "chaos"),
                    amount=listing.price_value,
                    confidence=1.0,
                    sample_size=1
                )
                prices.append(price_data)
        
        # Calculate statistics
        if prices:
            amounts = [p.amount for p in prices]
            avg_price = PriceData(
                currency=prices[0].currency,
                amount=mean(amounts),
                confidence=0.8,
                sample_size=len(prices)
            )
        else:
            avg_price = None
        
        return cls(
            item_type=item_type,
            league=league,
            total_listings=len(listings),
            average_price=avg_price,
            price_distribution=prices,
            data_quality=min(1.0, len(listings) / 100.0)  # Quality based on sample size
        )
