# PoE 2 Trade Valuation Tool Configuration

# API Configuration
api:
  # Your POESESSID cookie from pathofexile.com
  # You can find this in your browser's developer tools when logged in
  poesessid: "8646be150d5771916c09c68896a6bc9d"
  
  # Base URL for the trade API
  base_url: "https://www.pathofexile.com/api/trade2/"
  
  # Rate limiting (requests per minute)
  rate_limit: 60
  
  # Request timeout in seconds
  timeout: 30
  
  # User agent string
  user_agent: "PoE2 Trade Valuation Tool/0.1.0"

# League Configuration
league:
  # Default league to search in
  default: "Standard"
  
  # Available leagues (will be auto-detected from API)
  available:
    - "Standard"
    - "Hardcore"
    - "Rise of the Abyssal"
    - "HC Rise of the Abyssal"

# Search Configuration
search:
  # Default number of results to fetch
  default_limit: 100
  
  # Maximum number of results to process
  max_results: 1000
  
  # Only include online players
  online_only: true
  
  # Currency to filter by (optional)
  currency: "exalted"

# Valuation Configuration
valuation:
  # Minimum number of listings required for reliable valuation
  min_listings: 5
  
  # Percentile to use for price calculation (50 = median)
  price_percentile: 50
  
  # Weight recent listings more heavily
  time_decay_factor: 0.1
  
  # Minimum confidence score for valuations (0-1)
  min_confidence: 0.6

# Cache Configuration
cache:
  # Cache directory
  directory: "cache"
  
  # Cache expiry times (in minutes)
  market_data_expiry: 60
  modifier_values_expiry: 1440  # 24 hours
  
  # Maximum cache size (MB)
  max_size: 500

# Database Configuration
database:
  # Database file path
  path: "data/poe_trade.db"
  
  # Enable WAL mode for better performance
  wal_mode: true

# Logging Configuration
logging:
  # Log level (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"
  
  # Log file path
  file: "logs/poe_trade.log"
  
  # Enable console logging
  console: true
  
  # Log format
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# CLI Configuration
cli:
  # Default output format (table, json, csv)
  output_format: "table"
  
  # Enable colored output
  colored: true
  
  # Show progress bars
  show_progress: true
