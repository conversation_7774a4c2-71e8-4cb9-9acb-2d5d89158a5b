"""
CLI commands for historical data management and trend analysis.
"""

import click
import json
import csv
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from pathlib import Path

from ..config import load_config
from ..api import TradeAPIClient
from ..market import MarketDataCollector, MarketAnalyzer
from ..storage import (
    DatabaseManager, CacheManager, HistoricalMarketCollector,
    TrendAnalyzer, TimeFrame, TrendDirection
)


@click.group()
@click.pass_context
def historical(ctx):
    """Historical data management and trend analysis commands."""
    pass


@historical.command()
@click.option('--item-type', '-i', required=True, help='Item type to collect data for')
@click.option('--league', '-l', default='Standard', help='League to collect from')
@click.option('--max-results', '-m', default=200, help='Maximum number of listings to collect')
@click.pass_context
def collect(ctx, item_type, league, max_results):
    """Collect and store current market data for an item."""
    try:
        # Load configuration
        config_path = ctx.obj.get('config_path')
        settings = load_config(config_path)
        
        # Initialize components
        api_client = TradeAPIClient(
            poesessid=settings.api.poesessid,
            base_url=settings.api.base_url,
            rate_limit=settings.api.rate_limit,
            timeout=settings.api.timeout,
            user_agent=settings.api.user_agent
        )
        
        market_collector = MarketDataCollector(api_client)
        market_analyzer = MarketAnalyzer(
            min_listings=settings.valuation.min_listings,
            confidence_threshold=settings.valuation.min_confidence
        )
        
        database_manager = DatabaseManager(
            db_path=settings.database.db_path,
            wal_mode=settings.database.wal_mode
        )
        cache_manager = CacheManager(
            max_size_mb=settings.cache.max_size,
            default_ttl_seconds=settings.cache.market_data_expiry * 60
        )
        
        historical_collector = HistoricalMarketCollector(
            market_collector=market_collector,
            market_analyzer=market_analyzer,
            database_manager=database_manager,
            cache_manager=cache_manager
        )
        
        # Collect and store data
        click.echo(f"Collecting market data for {item_type} in {league}...")
        snapshot = historical_collector.collect_and_store_market_data(
            item_type=item_type,
            league=league,
            max_results=max_results
        )
        
        click.echo(f"✅ Successfully stored market snapshot {snapshot.id}")
        click.echo(f"   Base price: {snapshot.base_price} {snapshot.currency.value if snapshot.currency else 'N/A'}")
        click.echo(f"   Total listings: {snapshot.total_listings}")
        click.echo(f"   Data quality: {snapshot.data_quality_score:.2f}")
        click.echo(f"   Confidence: {snapshot.confidence_score:.2f}")
        
    except Exception as e:
        click.echo(f"❌ Error collecting data: {e}", err=True)
        raise click.Abort()


@historical.command()
@click.option('--item-type', '-i', required=True, help='Item type to analyze')
@click.option('--league', '-l', default='Standard', help='League to analyze')
@click.option('--days-back', '-d', default=30, help='Number of days to analyze')
@click.option('--output-format', '-f', type=click.Choice(['table', 'json']), default='table', help='Output format')
@click.pass_context
def analyze(ctx, item_type, league, days_back, output_format):
    """Analyze historical trends for an item."""
    try:
        # Load configuration and initialize components
        config_path = ctx.obj.get('config_path')
        settings = load_config(config_path)
        
        api_client = TradeAPIClient(
            poesessid=settings.api.poesessid,
            base_url=settings.api.base_url,
            rate_limit=settings.api.rate_limit,
            timeout=settings.api.timeout,
            user_agent=settings.api.user_agent
        )
        
        market_collector = MarketDataCollector(api_client)
        market_analyzer = MarketAnalyzer(
            min_listings=settings.valuation.min_listings,
            confidence_threshold=settings.valuation.min_confidence
        )
        
        database_manager = DatabaseManager(
            db_path=settings.database.db_path,
            wal_mode=settings.database.wal_mode
        )
        cache_manager = CacheManager(
            max_size_mb=settings.cache.max_size,
            default_ttl_seconds=settings.cache.market_data_expiry * 60
        )
        
        historical_collector = HistoricalMarketCollector(
            market_collector=market_collector,
            market_analyzer=market_analyzer,
            database_manager=database_manager,
            cache_manager=cache_manager
        )
        
        # Analyze trends
        click.echo(f"Analyzing trends for {item_type} in {league} ({days_back} days)...")
        trend_analysis = historical_collector.analyze_trends(
            item_type=item_type,
            league=league,
            days_back=days_back
        )
        
        if not trend_analysis:
            click.echo("❌ Insufficient historical data for trend analysis")
            raise click.Abort()
        
        if output_format == 'json':
            # Output as JSON
            analysis_data = {
                'item_type': trend_analysis.item_type,
                'league': trend_analysis.league,
                'overall_direction': trend_analysis.overall_direction.value,
                'trend_strength': trend_analysis.trend_strength,
                'trend_consistency': trend_analysis.trend_consistency,
                'is_trending': trend_analysis.is_trending,
                'momentum_score': trend_analysis.momentum_score,
                'support_level': trend_analysis.support_level,
                'resistance_level': trend_analysis.resistance_level,
                'buy_signal_strength': trend_analysis.buy_signal_strength,
                'sell_signal_strength': trend_analysis.sell_signal_strength,
                'hold_recommendation': trend_analysis.hold_recommendation,
                'analysis_confidence': trend_analysis.analysis_confidence,
                'data_coverage_days': trend_analysis.data_coverage_days
            }
            click.echo(json.dumps(analysis_data, indent=2))
        else:
            # Output as formatted table
            click.echo("\n📊 Trend Analysis Results")
            click.echo("=" * 50)
            click.echo(f"Item Type: {trend_analysis.item_type}")
            click.echo(f"League: {trend_analysis.league}")
            click.echo(f"Data Coverage: {trend_analysis.data_coverage_days} days")
            click.echo()
            
            # Overall assessment
            direction_color = {
                TrendDirection.RISING: 'green',
                TrendDirection.FALLING: 'red',
                TrendDirection.STABLE: 'yellow',
                TrendDirection.VOLATILE: 'magenta'
            }.get(trend_analysis.overall_direction, 'white')
            
            click.echo("📈 Overall Assessment:")
            click.echo(f"  Direction: {click.style(trend_analysis.overall_direction.value.upper(), fg=direction_color)}")
            click.echo(f"  Strength: {trend_analysis.trend_strength:.2%}")
            click.echo(f"  Consistency: {trend_analysis.trend_consistency:.2%}")
            click.echo(f"  Trending: {'Yes' if trend_analysis.is_trending else 'No'}")
            click.echo(f"  Momentum: {trend_analysis.momentum_score:.3f}")
            click.echo()
            
            # Trading signals
            click.echo("🎯 Trading Signals:")
            click.echo(f"  Buy Signal: {trend_analysis.buy_signal_strength:.2%}")
            click.echo(f"  Sell Signal: {trend_analysis.sell_signal_strength:.2%}")
            click.echo(f"  Hold Recommended: {'Yes' if trend_analysis.hold_recommendation else 'No'}")
            click.echo()
            
            # Support/Resistance
            if trend_analysis.support_level or trend_analysis.resistance_level:
                click.echo("📊 Key Levels:")
                if trend_analysis.support_level:
                    click.echo(f"  Support: {trend_analysis.support_level:.2f}")
                if trend_analysis.resistance_level:
                    click.echo(f"  Resistance: {trend_analysis.resistance_level:.2f}")
                click.echo()
            
            # Confidence
            confidence_color = 'green' if trend_analysis.analysis_confidence > 0.7 else 'yellow' if trend_analysis.analysis_confidence > 0.4 else 'red'
            click.echo(f"🎯 Analysis Confidence: {click.style(f'{trend_analysis.analysis_confidence:.2%}', fg=confidence_color)}")
        
    except Exception as e:
        click.echo(f"❌ Error analyzing trends: {e}", err=True)
        raise click.Abort()


@historical.command()
@click.option('--league', '-l', default='Standard', help='League to check')
@click.option('--min-change', '-c', default=10.0, help='Minimum price change percentage')
@click.option('--limit', '-n', default=20, help='Maximum number of items to show')
@click.option('--output-format', '-f', type=click.Choice(['table', 'json']), default='table', help='Output format')
@click.pass_context
def trending(ctx, league, min_change, limit, output_format):
    """Show currently trending items."""
    try:
        # Load configuration and initialize components
        config_path = ctx.obj.get('config_path')
        settings = load_config(config_path)
        
        database_manager = DatabaseManager(
            db_path=settings.database.db_path,
            wal_mode=settings.database.wal_mode
        )
        
        # Get trending items
        trending_items = database_manager.get_trending_items(
            league=league,
            min_change_percent=min_change,
            limit=limit
        )
        
        if not trending_items:
            click.echo(f"No trending items found in {league} with >{min_change}% change")
            return
        
        if output_format == 'json':
            click.echo(json.dumps(trending_items, indent=2))
        else:
            click.echo(f"\n🔥 Trending Items in {league}")
            click.echo("=" * 80)
            click.echo(f"{'Item Type':<30} {'Direction':<10} {'Change':<10} {'Price Range':<20} {'Listings':<10}")
            click.echo("-" * 80)
            
            for item in trending_items:
                direction_color = {
                    'rising': 'green',
                    'falling': 'red',
                    'stable': 'yellow',
                    'volatile': 'magenta'
                }.get(item['direction'], 'white')
                
                change_str = f"{item['price_change_percent']:+.1f}%"
                price_range = f"{item['start_price']:.1f} → {item['end_price']:.1f} {item['currency']}"
                
                click.echo(
                    f"{item['item_type']:<30} "
                    f"{click.style(item['direction'], fg=direction_color):<10} "
                    f"{change_str:<10} "
                    f"{price_range:<20} "
                    f"{item['average_listings']:.0f}"
                )
        
    except Exception as e:
        click.echo(f"❌ Error getting trending items: {e}", err=True)
        raise click.Abort()


@historical.command()
@click.option('--days-to-keep', '-d', default=90, help='Number of days of data to keep')
@click.option('--dry-run', is_flag=True, help='Show what would be deleted without actually deleting')
@click.pass_context
def cleanup(ctx, days_to_keep, dry_run):
    """Clean up old historical data."""
    try:
        # Load configuration
        config_path = ctx.obj.get('config_path')
        settings = load_config(config_path)
        
        database_manager = DatabaseManager(
            db_path=settings.database.db_path,
            wal_mode=settings.database.wal_mode
        )
        
        if dry_run:
            # Get stats before cleanup
            stats = database_manager.get_database_stats()
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            click.echo(f"🔍 Dry run - would delete data older than {cutoff_date.strftime('%Y-%m-%d')}")
            click.echo(f"Current database stats:")
            click.echo(f"  Market snapshots: {stats.get('market_snapshots_count', 0):,}")
            click.echo(f"  Modifier trends: {stats.get('modifier_trends_count', 0):,}")
            click.echo(f"  Price trends: {stats.get('price_trends_count', 0):,}")
            click.echo(f"  Database size: {stats.get('database_size_mb', 0):.1f} MB")
            click.echo("\nUse --no-dry-run to actually perform cleanup")
        else:
            click.echo(f"🧹 Cleaning up data older than {days_to_keep} days...")
            database_manager.cleanup_old_data(days_to_keep)
            click.echo("✅ Cleanup completed successfully")
            
            # Show updated stats
            stats = database_manager.get_database_stats()
            click.echo(f"Updated database size: {stats.get('database_size_mb', 0):.1f} MB")
        
    except Exception as e:
        click.echo(f"❌ Error during cleanup: {e}", err=True)
        raise click.Abort()


@historical.command()
@click.pass_context
def stats(ctx):
    """Show storage system statistics."""
    try:
        # Load configuration
        config_path = ctx.obj.get('config_path')
        settings = load_config(config_path)
        
        database_manager = DatabaseManager(
            db_path=settings.database.db_path,
            wal_mode=settings.database.wal_mode
        )
        
        stats = database_manager.get_database_stats()
        
        click.echo("\n📊 Storage System Statistics")
        click.echo("=" * 40)
        click.echo(f"Market Snapshots: {stats.get('market_snapshots_count', 0):,}")
        click.echo(f"Modifier Trends: {stats.get('modifier_trends_count', 0):,}")
        click.echo(f"Price Trends: {stats.get('price_trends_count', 0):,}")
        click.echo(f"Trend Analyses: {stats.get('trend_analyses_count', 0):,}")
        click.echo()
        click.echo(f"Database Size: {stats.get('database_size_mb', 0):.1f} MB")
        click.echo(f"Unique Items: {stats.get('unique_items', 0):,}")
        click.echo(f"Unique Leagues: {stats.get('unique_leagues', 0):,}")
        
        if 'data_range' in stats and stats['data_range']:
            click.echo()
            click.echo(f"Data Range:")
            click.echo(f"  Earliest: {stats['data_range']['earliest']}")
            click.echo(f"  Latest: {stats['data_range']['latest']}")
        
    except Exception as e:
        click.echo(f"❌ Error getting statistics: {e}", err=True)
        raise click.Abort()
