"""
Main CLI interface for the PoE 2 trade valuation tool.
"""

import click
import logging
from pathlib import Path
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

from ..config import load_config, create_example_config
from ..api import TradeAPIClient
from .historical import historical


console = Console()


def setup_logging(settings):
    """Set up logging configuration."""
    log_level = getattr(logging, settings.logging.level.upper())
    
    # Create formatter
    formatter = logging.Formatter(settings.logging.format)
    
    # Set up root logger
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # Console handler
    if settings.logging.console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if settings.logging.file:
        settings.logging.log_path.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(settings.logging.log_path)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)


@click.group()
@click.option('--config', '-c', help='Path to configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.pass_context
def cli(ctx, config, verbose):
    """PoE 2 Trade Valuation Tool - Analyze item values using market data."""
    ctx.ensure_object(dict)

    # Store config path and verbose flag for later use
    ctx.obj['config_path'] = config
    ctx.obj['verbose'] = verbose

    # Don't load config for init-config command
    if ctx.invoked_subcommand == 'init_config':
        return

    try:
        # Load configuration
        settings = load_config(config)
        ctx.obj['settings'] = settings

        # Set up logging
        if verbose:
            settings.logging.level = "DEBUG"
        setup_logging(settings)

        # Create API client
        api_client = TradeAPIClient(
            poesessid=settings.api.poesessid,
            base_url=settings.api.base_url,
            rate_limit=settings.api.rate_limit,
            timeout=settings.api.timeout,
            user_agent=settings.api.user_agent
        )
        ctx.obj['api_client'] = api_client

    except FileNotFoundError as e:
        console.print(f"[red]Error: {e}[/red]")
        console.print("\n[yellow]To get started:[/yellow]")
        console.print("1. Run 'poe-trade init-config' to create a config file")
        console.print("2. Edit config/config.yaml and set your POESESSID")
        console.print("3. Run the command again")
        ctx.exit(1)
    except Exception as e:
        console.print(f"[red]Configuration error: {e}[/red]")
        ctx.exit(1)


@cli.command()
@click.option('--output', '-o', default='config/config.yaml', help='Output path for config file')
def init_config(output):
    """Create an example configuration file."""
    try:
        create_example_config(output)
        console.print(f"[green]Created example config at {output}[/green]")
        console.print("\n[yellow]Next steps:[/yellow]")
        console.print("1. Edit the config file and set your POESESSID")
        console.print("2. Adjust other settings as needed")
        console.print("3. Run 'poe-trade test-connection' to verify setup")
    except Exception as e:
        console.print(f"[red]Error creating config: {e}[/red]")


@cli.command()
@click.pass_context
def test_connection(ctx):
    """Test connection to the PoE 2 trade API."""
    api_client = ctx.obj['api_client']
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Testing API connection...", total=None)
        
        try:
            # Test basic API access
            leagues = api_client.get_leagues()
            progress.update(task, description="✓ Connected to API")
            
            console.print("\n[green]✓ API connection successful![/green]")
            console.print(f"Found {len(leagues)} leagues:")
            
            for league in leagues[:5]:  # Show first 5 leagues
                console.print(f"  • {league.get('text', 'Unknown')}")
            
            if len(leagues) > 5:
                console.print(f"  ... and {len(leagues) - 5} more")
                
        except Exception as e:
            progress.update(task, description="✗ Connection failed")
            console.print(f"\n[red]✗ API connection failed: {e}[/red]")
            console.print("\n[yellow]Troubleshooting:[/yellow]")
            console.print("1. Check your POESESSID in the config file")
            console.print("2. Make sure you're logged into pathofexile.com")
            console.print("3. Verify your internet connection")


@cli.command()
@click.option('--league', '-l', help='League to search in')
@click.option('--item-type', '-t', help='Item type to search for')
@click.option('--limit', '-n', type=int, default=10, help='Number of results to show')
@click.pass_context
def search(ctx, league, item_type, limit):
    """Search for items in the trade market."""
    api_client = ctx.obj['api_client']
    settings = ctx.obj['settings']
    
    # Use default league if not specified
    if not league:
        league = settings.league.default
    
    if not item_type:
        console.print("[red]Error: --item-type is required[/red]")
        console.print("Example: poe-trade search --item-type 'Precursor Tablet'")
        return
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task(f"Searching for {item_type} in {league}...", total=None)
        
        try:
            # Create a basic search query
            query = {
                "query": {
                    "status": {"option": "online"},
                    "filters": {
                        "type_filters": {
                            "filters": {}
                        }
                    }
                }
            }
            
            # Add item type filter if it looks like a category
            if "tablet" in item_type.lower():
                # This is a simplified approach - in a full implementation,
                # we'd need to map item types to proper category IDs
                pass
            
            # Perform search
            search_result = api_client.search(league, query)
            item_ids = search_result.get('result', [])[:limit]
            
            if not item_ids:
                progress.update(task, description="No items found")
                console.print(f"[yellow]No items found for '{item_type}' in {league}[/yellow]")
                return
            
            progress.update(task, description=f"Found {len(item_ids)} items, fetching details...")
            
            # Fetch item details
            query_id = search_result.get('id', '')
            items_data = api_client.fetch_items(item_ids, query_id)
            
            progress.update(task, description="✓ Search completed")
            
            # Display results
            console.print(f"\n[green]Found {len(item_ids)} items:[/green]")
            
            table = Table(show_header=True, header_style="bold magenta")
            table.add_column("Item", style="cyan")
            table.add_column("Price", style="green")
            table.add_column("Seller", style="yellow")
            
            for item_data in items_data.get('result', []):
                item = item_data.get('item', {})
                listing = item_data.get('listing', {})
                
                item_name = item.get('name', 'Unknown')
                if not item_name:
                    item_name = item.get('typeLine', 'Unknown')
                
                price_info = listing.get('price', {})
                if price_info:
                    price = f"{price_info.get('amount', 0)} {price_info.get('currency', 'unknown')}"
                else:
                    price = "No price"
                
                account = item_data.get('listing', {}).get('account', {})
                seller = account.get('name', 'Unknown')
                
                table.add_row(item_name, price, seller)
            
            console.print(table)
            
        except Exception as e:
            progress.update(task, description="✗ Search failed")
            console.print(f"\n[red]✗ Search failed: {e}[/red]")


@cli.command()
@click.pass_context
def list_leagues(ctx):
    """List available leagues."""
    api_client = ctx.obj['api_client']
    
    try:
        leagues = api_client.get_leagues()
        
        console.print("[green]Available leagues:[/green]")
        table = Table(show_header=True, header_style="bold magenta")
        table.add_column("League", style="cyan")
        table.add_column("Realm", style="yellow")
        
        for league in leagues:
            table.add_row(
                league.get('text', 'Unknown'),
                league.get('realm', 'unknown')
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Error fetching leagues: {e}[/red]")


@cli.command()
@click.option('--item-type', '-t', default='Precursor Tablet', help='Item type to valuate')
@click.option('--league', '-l', help='League to analyze')
@click.pass_context
def valuate(ctx, item_type, league):
    """Analyze and valuate item modifiers (placeholder)."""
    settings = ctx.obj['settings']
    
    if not league:
        league = settings.league.default
    
    console.print(f"[yellow]Valuation feature coming soon![/yellow]")
    console.print(f"Will analyze {item_type} modifiers in {league}")
    console.print("\nThis will:")
    console.print("• Collect market data for the item type")
    console.print("• Analyze modifier values")
    console.print("• Generate a valuation report")


# Add historical data management commands
cli.add_command(historical)


if __name__ == '__main__':
    cli()
