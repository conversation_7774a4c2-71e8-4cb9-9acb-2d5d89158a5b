"""
Data models for items and modifiers.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator


class ItemRarity(str, Enum):
    """Item rarity levels."""
    NORMAL = "Normal"
    MAGIC = "Magic"
    RARE = "Rare"
    UNIQUE = "Unique"


class ModifierType(str, Enum):
    """Types of modifiers."""
    IMPLICIT = "implicit"
    EXPLICIT = "explicit"
    CRAFTED = "crafted"
    ENCHANT = "enchant"
    FRACTURED = "fractured"
    CORRUPTED = "corrupted"
    DESECRATED = "desecrated"


class Modifier(BaseModel):
    """Represents a single modifier on an item."""
    
    id: Optional[str] = None
    text: str
    type: ModifierType
    tier: Optional[int] = None
    values: List[Union[int, float]] = Field(default_factory=list)
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    
    @validator('values', pre=True)
    def parse_values(cls, v):
        """Parse modifier values from various formats."""
        if isinstance(v, (list, tuple)):
            return list(v)
        elif isinstance(v, (int, float)):
            return [v]
        elif isinstance(v, str):
            # Try to extract numbers from text
            import re
            numbers = re.findall(r'-?\d+(?:\.\d+)?', v)
            return [float(n) if '.' in n else int(n) for n in numbers]
        return []
    
    @property
    def value_range(self) -> Optional[tuple]:
        """Get the value range for this modifier."""
        if self.min_value is not None and self.max_value is not None:
            return (self.min_value, self.max_value)
        elif self.values:
            return (min(self.values), max(self.values))
        return None


class ItemSocket(BaseModel):
    """Represents an item socket."""
    
    group: int
    attr: Optional[str] = None  # S, D, I, G, A, DV for PoE1
    colour: Optional[str] = None  # R, G, B, W, A, DV for PoE1
    type: Optional[str] = None  # gem, jewel, rune for PoE2
    item: Optional[str] = None  # socket item type for PoE2


class ItemProperty(BaseModel):
    """Represents an item property."""
    
    name: str
    values: List[List[Union[str, int]]] = Field(default_factory=list)
    display_mode: Optional[int] = None
    progress: Optional[float] = None
    type: Optional[int] = None
    suffix: Optional[str] = None


class Item(BaseModel):
    """Base item model."""
    
    id: Optional[str] = None
    name: str
    type_line: str
    base_type: str
    rarity: ItemRarity = ItemRarity.NORMAL
    identified: bool = True
    item_level: int = 0
    
    # Modifiers
    implicit_mods: List[Modifier] = Field(default_factory=list)
    explicit_mods: List[Modifier] = Field(default_factory=list)
    crafted_mods: List[Modifier] = Field(default_factory=list)
    enchant_mods: List[Modifier] = Field(default_factory=list)
    fractured_mods: List[Modifier] = Field(default_factory=list)
    
    # Properties
    properties: List[ItemProperty] = Field(default_factory=list)
    requirements: List[ItemProperty] = Field(default_factory=list)
    
    # Sockets
    sockets: List[ItemSocket] = Field(default_factory=list)
    
    # Flags
    corrupted: bool = False
    sanctified: bool = False  # PoE2 specific
    
    # Additional data
    note: Optional[str] = None
    league: Optional[str] = None
    
    @property
    def all_modifiers(self) -> List[Modifier]:
        """Get all modifiers on this item."""
        return (
            self.implicit_mods + 
            self.explicit_mods + 
            self.crafted_mods + 
            self.enchant_mods + 
            self.fractured_mods
        )
    
    def get_modifiers_by_type(self, mod_type: ModifierType) -> List[Modifier]:
        """Get modifiers of a specific type."""
        type_map = {
            ModifierType.IMPLICIT: self.implicit_mods,
            ModifierType.EXPLICIT: self.explicit_mods,
            ModifierType.CRAFTED: self.crafted_mods,
            ModifierType.ENCHANT: self.enchant_mods,
            ModifierType.FRACTURED: self.fractured_mods,
        }
        return type_map.get(mod_type, [])


class PrecursorTablet(Item):
    """Specific model for Precursor Tablets."""
    
    tablet_type: Optional[str] = None  # Breach, Delirium, Expedition, etc.
    
    @validator('base_type')
    def validate_base_type(cls, v):
        """Ensure this is actually a precursor tablet."""
        if "Precursor Tablet" not in v:
            raise ValueError("Not a Precursor Tablet")
        return v
    
    @property
    def valuable_modifiers(self) -> List[Modifier]:
        """Get modifiers that are typically valuable on precursor tablets."""
        # This would be populated based on market analysis
        # For now, return all explicit modifiers
        return self.explicit_mods
