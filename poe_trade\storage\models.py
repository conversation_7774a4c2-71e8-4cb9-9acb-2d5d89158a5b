"""
Historical data models for market trend analysis.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from pydantic import BaseModel, Field, validator
from statistics import mean, median, stdev

from ..models import Currency, PriceData


class TrendDirection(str, Enum):
    """Price trend direction."""
    RISING = "rising"
    FALLING = "falling"
    STABLE = "stable"
    VOLATILE = "volatile"


class TimeFrame(str, Enum):
    """Time frame for trend analysis."""
    HOUR = "1h"
    DAY = "1d"
    WEEK = "1w"
    MONTH = "1m"
    QUARTER = "3m"
    YEAR = "1y"


class MarketSnapshot(BaseModel):
    """A snapshot of market data at a specific point in time."""
    
    id: Optional[int] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    item_type: str
    league: str
    
    # Price data
    base_price: Optional[float] = None
    currency: Optional[Currency] = None
    median_price: Optional[float] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    
    # Market statistics
    total_listings: int = 0
    active_listings: int = 0
    average_listing_age_hours: Optional[float] = None
    
    # Data quality metrics
    data_quality_score: float = Field(default=0.0, ge=0.0, le=1.0)
    sample_size: int = 0
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Market activity indicators
    price_volatility: Optional[float] = None  # Coefficient of variation
    market_activity_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Currency: lambda v: v.value if v else None
        }


class ModifierTrend(BaseModel):
    """Historical trend data for a specific modifier."""
    
    id: Optional[int] = None
    modifier_id: str
    modifier_text: str
    item_type: str
    league: str
    timestamp: datetime = Field(default_factory=datetime.now)
    
    # Price data
    base_value: Optional[float] = None
    currency: Optional[Currency] = None
    value_per_tier: Optional[float] = None
    value_per_point: Optional[float] = None
    
    # Statistical data
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    median_value: Optional[float] = None
    
    # Market data
    total_listings: int = 0
    sample_size: int = 0
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Currency: lambda v: v.value if v else None
        }


class PriceTrend(BaseModel):
    """Price trend analysis over a specific time period."""
    
    id: Optional[int] = None
    item_type: str
    league: str
    modifier_id: Optional[str] = None  # None for base item price
    
    # Time period
    start_time: datetime
    end_time: datetime
    time_frame: TimeFrame
    
    # Trend analysis
    direction: TrendDirection
    price_change_percent: float  # Percentage change over period
    price_change_absolute: float  # Absolute change in currency units
    currency: Currency
    
    # Statistical measures
    start_price: float
    end_price: float
    min_price: float
    max_price: float
    average_price: float
    median_price: float
    
    # Volatility measures
    price_volatility: float  # Standard deviation / mean
    max_drawdown: float  # Maximum price drop from peak
    max_gain: float  # Maximum price gain from trough
    
    # Data quality
    data_points: int  # Number of snapshots used
    confidence_score: float = Field(default=0.0, ge=0.0, le=1.0)
    
    # Market activity
    average_listings: float
    listing_velocity: float  # Change in listing count over time
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            Currency: lambda v: v.value,
            TrendDirection: lambda v: v.value,
            TimeFrame: lambda v: v.value
        }
    
    @validator('price_change_percent')
    def validate_price_change_percent(cls, v):
        """Ensure price change percent is reasonable."""
        if abs(v) > 1000:  # More than 1000% change seems unrealistic
            raise ValueError("Price change percent seems unrealistic")
        return v


class TrendAnalysis(BaseModel):
    """Comprehensive trend analysis for an item or modifier."""
    
    item_type: str
    league: str
    modifier_id: Optional[str] = None
    analysis_timestamp: datetime = Field(default_factory=datetime.now)
    
    # Multi-timeframe trends
    hourly_trend: Optional[PriceTrend] = None
    daily_trend: Optional[PriceTrend] = None
    weekly_trend: Optional[PriceTrend] = None
    monthly_trend: Optional[PriceTrend] = None
    
    # Overall assessment
    overall_direction: TrendDirection
    trend_strength: float = Field(default=0.0, ge=0.0, le=1.0)  # How strong the trend is
    trend_consistency: float = Field(default=0.0, ge=0.0, le=1.0)  # How consistent across timeframes
    
    # Market insights
    is_trending: bool = False  # Whether item is currently trending
    momentum_score: float = Field(default=0.0, ge=-1.0, le=1.0)  # Positive = bullish, negative = bearish
    support_level: Optional[float] = None  # Price support level
    resistance_level: Optional[float] = None  # Price resistance level
    
    # Recommendations
    buy_signal_strength: float = Field(default=0.0, ge=0.0, le=1.0)
    sell_signal_strength: float = Field(default=0.0, ge=0.0, le=1.0)
    hold_recommendation: bool = False
    
    # Data quality
    analysis_confidence: float = Field(default=0.0, ge=0.0, le=1.0)
    data_coverage_days: int = 0
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            TrendDirection: lambda v: v.value
        }


class HistoricalMarketData(BaseModel):
    """Container for historical market data with trend analysis capabilities."""
    
    item_type: str
    league: str
    snapshots: List[MarketSnapshot] = Field(default_factory=list)
    modifier_trends: Dict[str, List[ModifierTrend]] = Field(default_factory=dict)
    
    # Cached analysis results
    latest_analysis: Optional[TrendAnalysis] = None
    last_analysis_update: Optional[datetime] = None
    
    def add_snapshot(self, snapshot: MarketSnapshot):
        """Add a new market snapshot."""
        self.snapshots.append(snapshot)
        # Keep snapshots sorted by timestamp
        self.snapshots.sort(key=lambda x: x.timestamp)
    
    def add_modifier_trend(self, modifier_trend: ModifierTrend):
        """Add a modifier trend data point."""
        if modifier_trend.modifier_id not in self.modifier_trends:
            self.modifier_trends[modifier_trend.modifier_id] = []
        
        self.modifier_trends[modifier_trend.modifier_id].append(modifier_trend)
        # Keep trends sorted by timestamp
        self.modifier_trends[modifier_trend.modifier_id].sort(key=lambda x: x.timestamp)
    
    def get_snapshots_in_range(
        self, 
        start_time: datetime, 
        end_time: datetime
    ) -> List[MarketSnapshot]:
        """Get snapshots within a specific time range."""
        return [
            snapshot for snapshot in self.snapshots
            if start_time <= snapshot.timestamp <= end_time
        ]
    
    def get_latest_snapshot(self) -> Optional[MarketSnapshot]:
        """Get the most recent market snapshot."""
        return self.snapshots[-1] if self.snapshots else None
    
    def calculate_price_trend(
        self, 
        time_frame: TimeFrame,
        end_time: Optional[datetime] = None
    ) -> Optional[PriceTrend]:
        """Calculate price trend for a specific time frame."""
        if not self.snapshots:
            return None
        
        if end_time is None:
            end_time = datetime.now()
        
        # Calculate start time based on time frame
        time_deltas = {
            TimeFrame.HOUR: timedelta(hours=1),
            TimeFrame.DAY: timedelta(days=1),
            TimeFrame.WEEK: timedelta(weeks=1),
            TimeFrame.MONTH: timedelta(days=30),
            TimeFrame.QUARTER: timedelta(days=90),
            TimeFrame.YEAR: timedelta(days=365)
        }
        
        start_time = end_time - time_deltas[time_frame]
        relevant_snapshots = self.get_snapshots_in_range(start_time, end_time)
        
        if len(relevant_snapshots) < 2:
            return None
        
        # Extract prices (use median_price if available, otherwise base_price)
        prices = []
        timestamps = []
        
        for snapshot in relevant_snapshots:
            price = snapshot.median_price or snapshot.base_price
            if price is not None:
                prices.append(price)
                timestamps.append(snapshot.timestamp)
        
        if len(prices) < 2:
            return None
        
        # Calculate trend metrics
        start_price = prices[0]
        end_price = prices[-1]
        min_price = min(prices)
        max_price = max(prices)
        average_price = mean(prices)
        median_price = median(prices)
        
        price_change_absolute = end_price - start_price
        price_change_percent = (price_change_absolute / start_price) * 100 if start_price > 0 else 0
        
        # Determine trend direction
        if abs(price_change_percent) < 5:  # Less than 5% change
            direction = TrendDirection.STABLE
        elif price_change_percent > 0:
            direction = TrendDirection.RISING
        else:
            direction = TrendDirection.FALLING
        
        # Calculate volatility
        price_volatility = stdev(prices) / average_price if average_price > 0 and len(prices) > 1 else 0
        
        if price_volatility > 0.3:  # High volatility threshold
            direction = TrendDirection.VOLATILE
        
        # Calculate drawdown and gain
        max_drawdown = 0
        max_gain = 0
        peak_price = prices[0]
        trough_price = prices[0]
        
        for price in prices:
            if price > peak_price:
                peak_price = price
            else:
                drawdown = (peak_price - price) / peak_price if peak_price > 0 else 0
                max_drawdown = max(max_drawdown, drawdown)
            
            if price < trough_price:
                trough_price = price
            else:
                gain = (price - trough_price) / trough_price if trough_price > 0 else 0
                max_gain = max(max_gain, gain)
        
        # Calculate confidence based on data quality
        confidence_score = min(1.0, len(prices) / 20.0)  # Higher confidence with more data points
        
        # Calculate average listings and velocity
        listings = [s.total_listings for s in relevant_snapshots if s.total_listings > 0]
        average_listings = mean(listings) if listings else 0
        
        listing_velocity = 0
        if len(listings) > 1:
            listing_velocity = (listings[-1] - listings[0]) / len(listings)
        
        # Get currency from latest snapshot
        currency = relevant_snapshots[-1].currency or Currency.CHAOS
        
        return PriceTrend(
            item_type=self.item_type,
            league=self.league,
            start_time=start_time,
            end_time=end_time,
            time_frame=time_frame,
            direction=direction,
            price_change_percent=price_change_percent,
            price_change_absolute=price_change_absolute,
            currency=currency,
            start_price=start_price,
            end_price=end_price,
            min_price=min_price,
            max_price=max_price,
            average_price=average_price,
            median_price=median_price,
            price_volatility=price_volatility,
            max_drawdown=max_drawdown,
            max_gain=max_gain,
            data_points=len(prices),
            confidence_score=confidence_score,
            average_listings=average_listings,
            listing_velocity=listing_velocity
        )
