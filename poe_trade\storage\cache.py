"""
Cache manager for in-memory caching of frequently accessed data.
"""

import time
import logging
import threading
import pickle
import hashlib
from typing import Any, Optional, Dict, List, Callable, Union
from datetime import datetime, timedelta
from pathlib import Path
from collections import OrderedDict
import json

from .models import MarketSnapshot, ModifierTrend, PriceTrend, TrendAnalysis


logger = logging.getLogger(__name__)


class CacheEntry:
    """Represents a cached item with TTL and metadata."""
    
    def __init__(self, value: Any, ttl_seconds: int, size_bytes: int = 0):
        self.value = value
        self.created_at = time.time()
        self.ttl_seconds = ttl_seconds
        self.size_bytes = size_bytes
        self.access_count = 0
        self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        return time.time() - self.created_at > self.ttl_seconds
    
    def access(self) -> Any:
        """Access the cached value and update access statistics."""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.value
    
    def get_age_seconds(self) -> float:
        """Get the age of the cache entry in seconds."""
        return time.time() - self.created_at


class CacheManager:
    """In-memory cache manager with TTL and size limits."""
    
    def __init__(
        self,
        max_size_mb: int = 500,
        default_ttl_seconds: int = 3600,
        cleanup_interval_seconds: int = 300
    ):
        """
        Initialize cache manager.
        
        Args:
            max_size_mb: Maximum cache size in megabytes
            default_ttl_seconds: Default TTL for cache entries
            cleanup_interval_seconds: How often to run cleanup
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.default_ttl_seconds = default_ttl_seconds
        self.cleanup_interval_seconds = cleanup_interval_seconds
        
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._current_size_bytes = 0
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired_removals': 0
        }
        
        # Start background cleanup thread
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info(f"Cache manager initialized: max_size={max_size_mb}MB, ttl={default_ttl_seconds}s")
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate a cache key from arguments."""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _estimate_size(self, obj: Any) -> int:
        """Estimate the size of an object in bytes."""
        try:
            return len(pickle.dumps(obj))
        except Exception:
            # Fallback estimation
            if isinstance(obj, str):
                return len(obj.encode('utf-8'))
            elif isinstance(obj, (list, tuple)):
                return sum(self._estimate_size(item) for item in obj)
            elif isinstance(obj, dict):
                return sum(
                    self._estimate_size(k) + self._estimate_size(v)
                    for k, v in obj.items()
                )
            else:
                return 1024  # Default estimate
    
    def _evict_lru(self):
        """Evict least recently used items to make space."""
        with self._lock:
            while (self._current_size_bytes > self.max_size_bytes * 0.8 and 
                   len(self._cache) > 0):
                # Remove oldest item (LRU)
                key, entry = self._cache.popitem(last=False)
                self._current_size_bytes -= entry.size_bytes
                self._stats['evictions'] += 1
                logger.debug(f"Evicted cache entry: {key}")
    
    def _cleanup_expired(self):
        """Remove expired cache entries."""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                entry = self._cache.pop(key)
                self._current_size_bytes -= entry.size_bytes
                self._stats['expired_removals'] += 1
            
            if expired_keys:
                logger.debug(f"Removed {len(expired_keys)} expired cache entries")
    
    def _cleanup_loop(self):
        """Background cleanup loop."""
        while True:
            try:
                time.sleep(self.cleanup_interval_seconds)
                self._cleanup_expired()
                self._evict_lru()
            except Exception as e:
                logger.error(f"Error in cache cleanup: {e}")
    
    def get(self, key: str) -> Optional[Any]:
        """Get a value from cache."""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            if entry.is_expired():
                self._cache.pop(key)
                self._current_size_bytes -= entry.size_bytes
                self._stats['expired_removals'] += 1
                self._stats['misses'] += 1
                return None
            
            # Move to end (most recently used)
            self._cache.move_to_end(key)
            self._stats['hits'] += 1
            
            return entry.access()
    
    def set(
        self,
        key: str,
        value: Any,
        ttl_seconds: Optional[int] = None
    ):
        """Set a value in cache."""
        if ttl_seconds is None:
            ttl_seconds = self.default_ttl_seconds
        
        size_bytes = self._estimate_size(value)
        
        # Don't cache items that are too large
        if size_bytes > self.max_size_bytes * 0.1:
            logger.warning(f"Item too large for cache: {size_bytes} bytes")
            return
        
        with self._lock:
            # Remove existing entry if present
            if key in self._cache:
                old_entry = self._cache.pop(key)
                self._current_size_bytes -= old_entry.size_bytes
            
            # Create new entry
            entry = CacheEntry(value, ttl_seconds, size_bytes)
            self._cache[key] = entry
            self._current_size_bytes += size_bytes
            
            # Evict if necessary
            if self._current_size_bytes > self.max_size_bytes:
                self._evict_lru()
    
    def delete(self, key: str) -> bool:
        """Delete a specific cache entry."""
        with self._lock:
            if key in self._cache:
                entry = self._cache.pop(key)
                self._current_size_bytes -= entry.size_bytes
                return True
            return False
    
    def clear(self):
        """Clear all cache entries."""
        with self._lock:
            self._cache.clear()
            self._current_size_bytes = 0
            logger.info("Cache cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'entries': len(self._cache),
                'size_mb': self._current_size_bytes / (1024 * 1024),
                'max_size_mb': self.max_size_bytes / (1024 * 1024),
                'utilization_percent': (self._current_size_bytes / self.max_size_bytes * 100),
                'hit_rate_percent': hit_rate,
                'hits': self._stats['hits'],
                'misses': self._stats['misses'],
                'evictions': self._stats['evictions'],
                'expired_removals': self._stats['expired_removals']
            }
    
    def cache_market_snapshots(
        self,
        item_type: str,
        league: str,
        snapshots: List[MarketSnapshot],
        ttl_seconds: int = 1800  # 30 minutes
    ):
        """Cache market snapshots for an item."""
        key = f"snapshots:{item_type}:{league}"
        self.set(key, snapshots, ttl_seconds)
    
    def get_cached_market_snapshots(
        self,
        item_type: str,
        league: str
    ) -> Optional[List[MarketSnapshot]]:
        """Get cached market snapshots."""
        key = f"snapshots:{item_type}:{league}"
        return self.get(key)
    
    def cache_price_trend(
        self,
        trend: PriceTrend,
        ttl_seconds: int = 3600  # 1 hour
    ):
        """Cache a price trend."""
        key = f"trend:{trend.item_type}:{trend.league}:{trend.time_frame.value}"
        if trend.modifier_id:
            key += f":{trend.modifier_id}"
        self.set(key, trend, ttl_seconds)
    
    def get_cached_price_trend(
        self,
        item_type: str,
        league: str,
        time_frame: str,
        modifier_id: Optional[str] = None
    ) -> Optional[PriceTrend]:
        """Get cached price trend."""
        key = f"trend:{item_type}:{league}:{time_frame}"
        if modifier_id:
            key += f":{modifier_id}"
        return self.get(key)
    
    def cache_trend_analysis(
        self,
        analysis: TrendAnalysis,
        ttl_seconds: int = 7200  # 2 hours
    ):
        """Cache trend analysis."""
        key = f"analysis:{analysis.item_type}:{analysis.league}"
        if analysis.modifier_id:
            key += f":{analysis.modifier_id}"
        self.set(key, analysis, ttl_seconds)
    
    def get_cached_trend_analysis(
        self,
        item_type: str,
        league: str,
        modifier_id: Optional[str] = None
    ) -> Optional[TrendAnalysis]:
        """Get cached trend analysis."""
        key = f"analysis:{item_type}:{league}"
        if modifier_id:
            key += f":{modifier_id}"
        return self.get(key)
    
    def cached_call(
        self,
        func: Callable,
        ttl_seconds: Optional[int] = None,
        *args,
        **kwargs
    ) -> Any:
        """Cache the result of a function call."""
        cache_key = f"func:{func.__name__}:{self._generate_key(*args, **kwargs)}"
        
        # Try to get from cache first
        result = self.get(cache_key)
        if result is not None:
            return result
        
        # Call function and cache result
        result = func(*args, **kwargs)
        self.set(cache_key, result, ttl_seconds)
        
        return result
