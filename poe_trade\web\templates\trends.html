{% extends "base.html" %}

{% block title %}Historical Market Trends - PoE 2 Trade Valuation{% endblock %}

{% block extra_css %}
<style>
    .trend-card {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border: 1px solid #444;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .trend-card:hover {
        border-color: #ffc107;
        box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
    }
    
    .trend-indicator {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .trend-rising { background-color: #28a745; color: white; }
    .trend-falling { background-color: #dc3545; color: white; }
    .trend-stable { background-color: #6c757d; color: white; }
    .trend-volatile { background-color: #fd7e14; color: white; }
    
    .signal-strength {
        width: 100%;
        height: 8px;
        background-color: #333;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .signal-bar {
        height: 100%;
        transition: width 0.3s ease;
    }
    
    .signal-buy { background-color: #28a745; }
    .signal-sell { background-color: #dc3545; }
    
    .price-chart {
        height: 300px;
        background: #1a1a1a;
        border: 1px solid #444;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #888;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }
    
    .stat-item {
        background: #2d2d2d;
        padding: 15px;
        border-radius: 6px;
        text-align: center;
    }
    
    .stat-value {
        font-size: 1.5em;
        font-weight: bold;
        color: #ffc107;
    }
    
    .stat-label {
        font-size: 0.9em;
        color: #ccc;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-warning">
                    <i class="bi bi-graph-up"></i> Historical Market Trends
                </h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-warning" onclick="refreshTrendingItems()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <a href="/" class="btn btn-secondary">
                        <i class="bi bi-house"></i> Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="bi bi-search"></i> Search & Analyze Trends</h5>
                </div>
                <div class="card-body">
                    <form id="trendsForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="itemType" class="form-label">Item Type</label>
                                <input type="text" class="form-control bg-dark text-light border-secondary" 
                                       id="itemType" placeholder="e.g., Precursor Tablet" required>
                            </div>
                            <div class="col-md-2">
                                <label for="league" class="form-label">League</label>
                                <select class="form-select bg-dark text-light border-secondary" id="league">
                                    {% for league in leagues %}
                                    <option value="{{ league }}" {% if league == default_league %}selected{% endif %}>{{ league }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="daysBack" class="form-label">Days Back</label>
                                <select class="form-select bg-dark text-light border-secondary" id="daysBack">
                                    <option value="7">7 days</option>
                                    <option value="30" selected>30 days</option>
                                    <option value="90">90 days</option>
                                    <option value="365">1 year</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-warning" id="analyzeBtn">
                                        <i class="bi bi-graph-up"></i> Analyze Trends
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Trending Items -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-dark border-secondary">
                <div class="card-header">
                    <h5 class="mb-0 text-warning"><i class="bi bi-fire"></i> Currently Trending Items</h5>
                </div>
                <div class="card-body">
                    <div id="trendingItems">
                        <div class="text-center text-muted">
                            <i class="bi bi-hourglass-split"></i> Loading trending items...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analysis Results -->
    <div id="analysisResults" style="display: none;">
        <div class="row">
            <div class="col-md-8">
                <!-- Price Chart -->
                <div class="card bg-dark border-secondary mb-4">
                    <div class="card-header">
                        <h5 class="mb-0 text-warning"><i class="bi bi-graph-up"></i> Price History</h5>
                    </div>
                    <div class="card-body">
                        <div id="priceChart" class="price-chart">
                            Price chart will be displayed here
                        </div>
                    </div>
                </div>

                <!-- Trend Analysis -->
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h5 class="mb-0 text-warning"><i class="bi bi-activity"></i> Trend Analysis</h5>
                    </div>
                    <div class="card-body" id="trendAnalysis">
                        <!-- Trend analysis content will be populated here -->
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Current Snapshot -->
                <div class="card bg-dark border-secondary mb-4">
                    <div class="card-header">
                        <h5 class="mb-0 text-warning"><i class="bi bi-camera"></i> Current Market</h5>
                    </div>
                    <div class="card-body" id="currentSnapshot">
                        <!-- Current snapshot will be populated here -->
                    </div>
                </div>

                <!-- Trading Signals -->
                <div class="card bg-dark border-secondary">
                    <div class="card-header">
                        <h5 class="mb-0 text-warning"><i class="bi bi-bullseye"></i> Trading Signals</h5>
                    </div>
                    <div class="card-body" id="tradingSignals">
                        <!-- Trading signals will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Storage Statistics -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card bg-dark border-secondary">
                <div class="card-header">
                    <h5 class="mb-0 text-warning"><i class="bi bi-database"></i> Storage Statistics</h5>
                </div>
                <div class="card-body">
                    <div id="storageStats" class="stats-grid">
                        <div class="text-center text-muted">
                            <i class="bi bi-hourglass-split"></i> Loading statistics...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-dark text-light">
            <strong class="me-auto">Notification</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
        </div>
        <div class="toast-body bg-dark text-light" id="toastBody">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadTrendingItems();
        loadStorageStats();
        setupEventListeners();
    });

    function setupEventListeners() {
        document.getElementById('trendsForm').addEventListener('submit', function(e) {
            e.preventDefault();
            analyzeHistoricalTrends();
        });
    }

    async function analyzeHistoricalTrends() {
        const itemType = document.getElementById('itemType').value.trim();
        const league = document.getElementById('league').value;
        const daysBack = parseInt(document.getElementById('daysBack').value);

        if (!itemType) {
            showToast('Please enter an item type', 'error');
            return;
        }

        const analyzeBtn = document.getElementById('analyzeBtn');
        analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Analyzing...';
        analyzeBtn.disabled = true;

        try {
            const response = await fetch('/api/analyze-historical', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    item_type: itemType,
                    league: league,
                    days_back: daysBack
                })
            });

            const data = await response.json();

            if (data.success) {
                displayAnalysisResults(data);
                showToast('Historical analysis completed successfully', 'success');
            } else {
                showToast(data.error, 'error');
            }
        } catch (error) {
            showToast('Analysis failed: ' + error.message, 'error');
        } finally {
            analyzeBtn.innerHTML = '<i class="bi bi-graph-up"></i> Analyze Trends';
            analyzeBtn.disabled = false;
        }
    }

    function displayAnalysisResults(data) {
        // Show results section
        document.getElementById('analysisResults').style.display = 'block';

        // Display current snapshot
        displayCurrentSnapshot(data.current_snapshot);

        // Display trend analysis
        displayTrendAnalysis(data.trend_analysis, data.timeframe_trends);

        // Display trading signals
        displayTradingSignals(data.trend_analysis);

        // Scroll to results
        document.getElementById('analysisResults').scrollIntoView({ behavior: 'smooth' });
    }

    function displayCurrentSnapshot(snapshot) {
        const container = document.getElementById('currentSnapshot');
        const price = snapshot.base_price ? `${snapshot.base_price} ${snapshot.currency || 'chaos'}` : 'No price data';
        
        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">${price}</div>
                    <div class="stat-label">Current Price</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${snapshot.total_listings}</div>
                    <div class="stat-label">Total Listings</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(snapshot.data_quality * 100).toFixed(1)}%</div>
                    <div class="stat-label">Data Quality</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${(snapshot.confidence * 100).toFixed(1)}%</div>
                    <div class="stat-label">Confidence</div>
                </div>
            </div>
            <small class="text-muted">Last updated: ${new Date(snapshot.timestamp).toLocaleString()}</small>
        `;
    }

    function displayTrendAnalysis(analysis, timeframes) {
        const container = document.getElementById('trendAnalysis');
        
        const directionClass = `trend-${analysis.overall_direction}`;
        const strengthPercent = (analysis.trend_strength * 100).toFixed(1);
        const consistencyPercent = (analysis.trend_consistency * 100).toFixed(1);
        
        let timeframeHtml = '';
        for (const [period, trend] of Object.entries(timeframes)) {
            if (trend) {
                const changeClass = trend.price_change_percent >= 0 ? 'text-success' : 'text-danger';
                const changeIcon = trend.price_change_percent >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';
                
                timeframeHtml += `
                    <div class="col-md-6 mb-3">
                        <div class="trend-card">
                            <h6 class="text-capitalize">${period} Trend</h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="trend-indicator trend-${trend.direction}">${trend.direction}</span>
                                <span class="${changeClass}">
                                    <i class="bi ${changeIcon}"></i> ${Math.abs(trend.price_change_percent).toFixed(2)}%
                                </span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    ${trend.start_price.toFixed(2)} → ${trend.end_price.toFixed(2)}
                                    (Confidence: ${(trend.confidence_score * 100).toFixed(1)}%)
                                </small>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        container.innerHTML = `
            <div class="mb-4">
                <h6>Overall Assessment</h6>
                <div class="d-flex align-items-center gap-3 mb-3">
                    <span class="trend-indicator ${directionClass}">${analysis.overall_direction}</span>
                    <span>Strength: ${strengthPercent}%</span>
                    <span>Consistency: ${consistencyPercent}%</span>
                    ${analysis.is_trending ? '<span class="badge bg-warning text-dark">TRENDING</span>' : ''}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="stat-item">
                            <div class="stat-value">${analysis.momentum_score.toFixed(3)}</div>
                            <div class="stat-label">Momentum Score</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stat-item">
                            <div class="stat-value">${analysis.data_coverage_days}</div>
                            <div class="stat-label">Days of Data</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <h6>Timeframe Analysis</h6>
            <div class="row">
                ${timeframeHtml}
            </div>
        `;
    }

    function displayTradingSignals(analysis) {
        const container = document.getElementById('tradingSignals');
        
        const buyPercent = (analysis.buy_signal_strength * 100).toFixed(1);
        const sellPercent = (analysis.sell_signal_strength * 100).toFixed(1);
        
        const supportLevel = analysis.support_level ? analysis.support_level.toFixed(2) : 'N/A';
        const resistanceLevel = analysis.resistance_level ? analysis.resistance_level.toFixed(2) : 'N/A';
        
        container.innerHTML = `
            <div class="mb-3">
                <label class="form-label">Buy Signal Strength</label>
                <div class="signal-strength">
                    <div class="signal-bar signal-buy" style="width: ${buyPercent}%"></div>
                </div>
                <small class="text-muted">${buyPercent}%</small>
            </div>
            
            <div class="mb-3">
                <label class="form-label">Sell Signal Strength</label>
                <div class="signal-strength">
                    <div class="signal-bar signal-sell" style="width: ${sellPercent}%"></div>
                </div>
                <small class="text-muted">${sellPercent}%</small>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">${supportLevel}</div>
                    <div class="stat-label">Support Level</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${resistanceLevel}</div>
                    <div class="stat-label">Resistance Level</div>
                </div>
            </div>
            
            ${analysis.hold_recommendation ? 
                '<div class="alert alert-warning mt-3"><i class="bi bi-pause-circle"></i> Recommendation: HOLD</div>' : 
                ''}
        `;
    }

    async function loadTrendingItems() {
        try {
            const league = document.getElementById('league').value;
            const response = await fetch(`/api/trending-items?league=${league}&limit=10`);
            const data = await response.json();

            if (data.success) {
                displayTrendingItems(data.trending_items);
            } else {
                document.getElementById('trendingItems').innerHTML = 
                    '<div class="text-center text-muted">No trending items found</div>';
            }
        } catch (error) {
            document.getElementById('trendingItems').innerHTML = 
                '<div class="text-center text-danger">Error loading trending items</div>';
        }
    }

    function displayTrendingItems(items) {
        const container = document.getElementById('trendingItems');
        
        if (!items || items.length === 0) {
            container.innerHTML = '<div class="text-center text-muted">No trending items found</div>';
            return;
        }

        const itemsHtml = items.map(item => {
            const changeClass = item.price_change_percent >= 0 ? 'text-success' : 'text-danger';
            const changeIcon = item.price_change_percent >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';
            
            return `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="trend-card">
                        <h6 class="text-truncate">${item.item_type}</h6>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="trend-indicator trend-${item.direction}">${item.direction}</span>
                            <span class="${changeClass}">
                                <i class="bi ${changeIcon}"></i> ${Math.abs(item.price_change_percent).toFixed(1)}%
                            </span>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                ${item.start_price.toFixed(2)} → ${item.end_price.toFixed(2)} ${item.currency}
                                <br>Listings: ${item.average_listings.toFixed(0)}
                            </small>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = `<div class="row">${itemsHtml}</div>`;
    }

    async function loadStorageStats() {
        try {
            const response = await fetch('/api/storage-stats');
            const data = await response.json();

            if (data.success) {
                displayStorageStats(data.stats);
            }
        } catch (error) {
            console.error('Error loading storage stats:', error);
        }
    }

    function displayStorageStats(stats) {
        const container = document.getElementById('storageStats');
        
        container.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${stats.storage_system.total_snapshots.toLocaleString()}</div>
                <div class="stat-label">Market Snapshots</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.storage_system.total_modifier_trends.toLocaleString()}</div>
                <div class="stat-label">Modifier Trends</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.storage_system.cache_hit_rate.toFixed(1)}%</div>
                <div class="stat-label">Cache Hit Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${stats.storage_system.database_size_mb.toFixed(1)} MB</div>
                <div class="stat-label">Database Size</div>
            </div>
        `;
    }

    function refreshTrendingItems() {
        document.getElementById('trendingItems').innerHTML = 
            '<div class="text-center text-muted"><i class="bi bi-hourglass-split"></i> Loading trending items...</div>';
        loadTrendingItems();
    }

    function showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const toastBody = document.getElementById('toastBody');
        
        toastBody.textContent = message;
        
        // Remove existing classes and add new ones based on type
        toast.className = 'toast';
        if (type === 'success') {
            toast.classList.add('bg-success');
        } else if (type === 'error') {
            toast.classList.add('bg-danger');
        } else {
            toast.classList.add('bg-info');
        }
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
    }
</script>
{% endblock %}
