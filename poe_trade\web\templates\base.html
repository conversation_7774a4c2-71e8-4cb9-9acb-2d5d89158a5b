<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PoE 2 Trade Valuation Tool{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --poe-gold: #d4af37;
            --poe-gold-light: #f4d03f;
            --poe-dark: #1e1e1e;
            --poe-darker: #121212;
            --poe-card: #2a2a2a;
            --poe-border: #404040;
            --poe-blue: #5dade2;
            --poe-green: #58d68d;
            --poe-red: #ec7063;
            --poe-purple: #af7ac5;
            --text-primary: #f8f9fa;
            --text-secondary: #dee2e6;
            --text-muted: #adb5bd;
        }

        body {
            background: linear-gradient(135deg, var(--poe-darker) 0%, var(--poe-dark) 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.6;
        }
        
        .navbar {
            background: var(--poe-card) !important;
            backdrop-filter: blur(10px);
            border-bottom: 2px solid var(--poe-gold);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .navbar-brand {
            color: var(--poe-gold-light) !important;
            font-weight: bold;
            font-size: 1.5rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        }

        .card {
            background: var(--poe-card);
            border: 1px solid var(--poe-border);
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            background: rgba(212, 175, 55, 0.15);
            border-bottom: 1px solid var(--poe-border);
            color: var(--poe-gold-light);
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--poe-blue), #357abd);
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #357abd, var(--poe-blue));
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(45deg, var(--poe-green), #449d44);
            border: none;
        }
        
        .btn-warning {
            background: linear-gradient(45deg, var(--poe-gold), #b8941f);
            border: none;
            color: var(--poe-dark);
            font-weight: 600;
            text-shadow: none;
        }

        .btn-warning:hover {
            background: linear-gradient(45deg, #b8941f, var(--poe-gold));
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(212, 175, 55, 0.4);
            color: var(--poe-dark);
        }

        .btn-outline-warning {
            border-color: var(--poe-gold);
            color: var(--poe-gold);
        }

        .btn-outline-warning:hover {
            background-color: var(--poe-gold);
            border-color: var(--poe-gold);
            color: var(--poe-dark);
        }
        
        .form-control, .form-select {
            background: #3a3a3a;
            border: 1px solid var(--poe-border);
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-control:focus, .form-select:focus {
            background: #404040;
            border-color: var(--poe-gold);
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
            color: var(--text-primary);
        }

        .form-label {
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .table-dark {
            --bs-table-bg: var(--poe-card);
            --bs-table-border-color: var(--poe-border);
            --bs-table-color: var(--text-primary);
        }

        .table-dark th {
            background: rgba(212, 175, 55, 0.1);
            color: var(--poe-gold-light);
            font-weight: 600;
            border-color: var(--poe-border);
        }
        
        .alert-info {
            background: rgba(74, 144, 226, 0.1);
            border-color: rgba(74, 144, 226, 0.3);
            color: #b3d9ff;
        }
        
        .alert-success {
            background: rgba(92, 184, 92, 0.1);
            border-color: rgba(92, 184, 92, 0.3);
            color: #b3ffb3;
        }
        
        .alert-danger {
            background: rgba(217, 83, 79, 0.1);
            border-color: rgba(217, 83, 79, 0.3);
            color: #ffb3b3;
        }
        
        .spinner-border {
            color: var(--poe-gold);
        }
        
        .text-gold {
            color: var(--poe-gold) !important;
        }
        
        .text-rarity-normal { color: #d0d0d0; }
        .text-rarity-magic { color: #7fb3ff; }
        .text-rarity-rare { color: #ffff88; }
        .text-rarity-unique { color: #d2691e; }

        .modifier-explicit { color: #7fb3ff; }
        .modifier-implicit { color: #d0d0d0; }
        .modifier-crafted { color: #c4c4ff; }

        .modifier-value-high { color: var(--poe-green); font-weight: 600; }
        .modifier-value-medium { color: var(--poe-gold); font-weight: 500; }
        .modifier-value-low { color: var(--text-muted); }
        .modifier-value-none { color: var(--poe-red); }

        /* Enhanced readability */
        .text-gold { color: var(--poe-gold-light) !important; }
        .text-muted { color: var(--text-muted) !important; }

        /* Better contrast for small text */
        small, .small {
            color: var(--text-secondary);
        }

        /* Improved badge contrast */
        .badge {
            font-size: 0.8em;
            font-weight: 500;
        }

        /* Glass effect improvements */
        .glass-effect {
            background: rgba(42, 42, 42, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid var(--poe-border);
        }

        /* Alert improvements */
        .alert {
            border: 1px solid var(--poe-border);
            background: rgba(42, 42, 42, 0.8);
        }

        .alert-info {
            border-color: var(--poe-blue);
            background: rgba(93, 173, 226, 0.1);
            color: var(--text-primary);
        }

        .alert-warning {
            border-color: var(--poe-gold);
            background: rgba(212, 175, 55, 0.1);
            color: var(--text-primary);
        }

        /* Fade in animation */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .price-high { color: var(--poe-green); }
        .price-medium { color: var(--poe-gold); }
        .price-low { color: #ff9999; }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-gem"></i> PoE 2 Trade Valuation
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/precursor-analysis">
                            <i class="bi bi-gem"></i> Precursor Analysis
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/tasks">
                            <i class="bi bi-list-task"></i> Task Manager
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/config">
                            <i class="bi bi-gear"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <button class="btn btn-outline-warning btn-sm ms-2" onclick="testConnection()">
                            <i class="bi bi-wifi"></i> Test Connection
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="toast" class="toast" role="alert">
            <div class="toast-header">
                <i class="bi bi-info-circle me-2"></i>
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body" id="toast-body">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Toast notification function
        function showToast(message, type = 'info') {
            const toast = document.getElementById('toast');
            const toastBody = document.getElementById('toast-body');
            const toastHeader = toast.querySelector('.toast-header');
            
            // Set message
            toastBody.textContent = message;
            
            // Set icon and color based on type
            const icon = toastHeader.querySelector('i');
            icon.className = `bi me-2 ${type === 'success' ? 'bi-check-circle text-success' : 
                                      type === 'error' ? 'bi-exclamation-triangle text-danger' : 
                                      'bi-info-circle text-info'}`;
            
            // Show toast
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        }
        
        // Test API connection
        async function testConnection() {
            try {
                const response = await fetch('/api/test-connection');
                const data = await response.json();
                
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast(data.error, 'error');
                }
            } catch (error) {
                showToast('Failed to test connection: ' + error.message, 'error');
            }
        }
        
        // Loading state management
        function setLoading(elementId, isLoading) {
            const element = document.getElementById(elementId);
            if (isLoading) {
                element.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
                element.disabled = true;
            } else {
                element.disabled = false;
            }
        }
        
        // Format currency display
        function formatPrice(amount, currency) {
            if (!amount || !currency) return 'No price';
            return `${amount} ${currency}`;
        }
        
        // Format rarity class
        function getRarityClass(rarity) {
            const rarityMap = {
                'Normal': 'text-rarity-normal',
                'Magic': 'text-rarity-magic', 
                'Rare': 'text-rarity-rare',
                'Unique': 'text-rarity-unique'
            };
            return rarityMap[rarity] || 'text-rarity-normal';
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
