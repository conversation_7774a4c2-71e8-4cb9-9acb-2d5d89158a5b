"""
Main API client for interacting with the PoE 2 trade API.
"""

import json
import time
import logging
from typing import Dict, List, Optional, Any, Union
from urllib.parse import urljoin

import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from .exceptions import (
    APIError, RateLimitError, AuthenticationError, 
    InvalidRequestError, ServerError, NetworkError
)
from .rate_limiter import SyncRateLimiter


logger = logging.getLogger(__name__)


class TradeAPIClient:
    """Client for interacting with the PoE 2 trade API."""
    
    def __init__(
        self,
        poesessid: str,
        base_url: str = "https://www.pathofexile.com/api/trade2/",
        rate_limit: int = 60,
        timeout: int = 30,
        user_agent: str = "PoE2 Trade Valuation Tool/0.1.0"
    ):
        """
        Initialize the trade API client.
        
        Args:
            poesessid: POESESSID cookie value
            base_url: Base URL for the trade API
            rate_limit: Maximum requests per minute
            timeout: Request timeout in seconds
            user_agent: User agent string
        """
        self.poesessid = poesessid
        self.base_url = base_url
        self.timeout = timeout
        self.user_agent = user_agent
        
        # Set up rate limiter
        self.rate_limiter = SyncRateLimiter(max_requests=rate_limit, time_window=60)
        
        # Set up session with retry strategy
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': self.user_agent,
            'Cookie': f'POESESSID={self.poesessid}',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        })
    
    def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None,
        params: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """
        Make a request to the API with rate limiting and error handling.
        
        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            
        Returns:
            Response data as dictionary
            
        Raises:
            Various API exceptions based on response
        """
        # Apply rate limiting
        self.rate_limiter.acquire()
        
        url = urljoin(self.base_url, endpoint)
        
        try:
            logger.debug(f"Making {method} request to {url}")
            
            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, params=params, timeout=self.timeout)
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            # Handle different response codes
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                raise AuthenticationError("Invalid POESESSID or authentication failed")
            elif response.status_code == 429:
                retry_after = int(response.headers.get('Retry-After', 60))
                raise RateLimitError(f"Rate limit exceeded", retry_after=retry_after)
            elif response.status_code == 400:
                raise InvalidRequestError(f"Bad request: {response.text}")
            elif response.status_code >= 500:
                raise ServerError(f"Server error: {response.status_code}")
            else:
                raise APIError(f"Unexpected status code: {response.status_code}", 
                             status_code=response.status_code)
                
        except requests.exceptions.Timeout:
            raise NetworkError("Request timed out")
        except requests.exceptions.ConnectionError:
            raise NetworkError("Connection error")
        except requests.exceptions.RequestException as e:
            raise NetworkError(f"Request failed: {str(e)}")
        except json.JSONDecodeError:
            raise APIError("Invalid JSON response")
    
    def get_leagues(self) -> List[Dict[str, Any]]:
        """Get available leagues."""
        response = self._make_request('GET', 'data/leagues')
        return response.get('result', [])
    
    def get_stats(self) -> List[Dict[str, Any]]:
        """Get available stats/modifiers."""
        response = self._make_request('GET', 'data/stats')
        return response.get('result', [])
    
    def get_items(self) -> List[Dict[str, Any]]:
        """Get available item types."""
        response = self._make_request('GET', 'data/items')
        return response.get('result', [])
    
    def search(self, league: str, query: Dict[str, Any]) -> Dict[str, Any]:
        """
        Search for items in the trade market.
        
        Args:
            league: League name (e.g., "Standard")
            query: Search query parameters
            
        Returns:
            Search results with item IDs
        """
        endpoint = f"search/poe2/{league}"
        return self._make_request('POST', endpoint, data=query)
    
    def fetch_items(self, item_ids: List[str], query_id: str) -> Dict[str, Any]:
        """
        Fetch detailed item information.
        
        Args:
            item_ids: List of item IDs to fetch
            query_id: Query ID from search results
            
        Returns:
            Detailed item data
        """
        # Limit to 10 items per request (API limitation)
        if len(item_ids) > 10:
            item_ids = item_ids[:10]
        
        item_ids_str = ",".join(item_ids)
        endpoint = f"fetch/{item_ids_str}"
        params = {"query": query_id}
        
        return self._make_request('GET', endpoint, params=params)
