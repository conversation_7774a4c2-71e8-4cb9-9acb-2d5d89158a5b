#!/usr/bin/env python3
"""
Test script to verify API connection and data collection.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poe_trade.config import load_config
from poe_trade.api import TradeAPIClient
from poe_trade.market import MarketDataCollector
from poe_trade.data.precursor_modifiers import TABLET_TYPES

def test_api_connection():
    """Test basic API connection."""
    print("🔌 Testing API Connection")
    print("=" * 50)
    
    try:
        # Load config
        config = load_config()
        if not config:
            print("❌ Failed to load configuration")
            return False
        
        print("✅ Configuration loaded successfully")
        
        # Test API client
        client = TradeAPIClient(
            poesessid=config.api.poesessid,
            user_agent=config.api.user_agent
        )
        
        # Test leagues
        leagues = client.get_leagues()
        print(f"✅ Found {len(leagues)} leagues")

        # Debug: print league structure
        if leagues:
            print(f"Sample league data: {leagues[0]}")
            print(f"League type: {type(leagues[0])}")

        # Check if Rise Of The Abyssal is available
        abyssal_league = None
        league_names = []

        for league in leagues:
            if isinstance(league, dict):
                league_id = league.get('id', str(league))
            else:
                league_id = getattr(league, 'id', str(league))

            league_names.append(league_id)
            if "Rise of the Abyssal" in league_id:
                abyssal_league = league

        print(f"Available leagues: {league_names}")

        if abyssal_league:
            print(f"✅ Found 'Rise Of The Abyssal' league")
        else:
            print("⚠️  'Rise Of The Abyssal' league not found")
        
        return True
        
    except Exception as e:
        print(f"❌ API connection failed: {e}")
        return False

def test_data_collection():
    """Test data collection for a single tablet type."""
    print("\n\n📊 Testing Data Collection")
    print("=" * 50)
    
    try:
        # Initialize components
        config = load_config()
        client = TradeAPIClient(
            poesessid=config.api.poesessid,
            user_agent=config.api.user_agent
        )
        collector = MarketDataCollector(client)
        
        # Test with a single tablet type first
        test_tablet = "Breach Precursor Tablet"
        league = "Rise of the Abyssal"
        
        print(f"Testing collection for: {test_tablet}")
        print(f"League: {league}")
        
        # Collect data
        tablet_data = collector.collect_precursor_tablet_data(
            league=league,
            tablet_types=[test_tablet],
            max_results=10  # Small sample for testing
        )
        
        if test_tablet in tablet_data:
            listings = tablet_data[test_tablet]
            print(f"✅ Found {len(listings)} listings for {test_tablet}")
            
            if listings:
                sample = listings[0]
                print(f"\nSample listing:")
                print(f"  Item: {sample.item.name if sample.item else 'No item'}")
                print(f"  Price: {sample.price_value if sample.price_value else 'No price'} {sample.price_currency if sample.price_currency else ''}")
                
                if sample.item and sample.item.all_modifiers:
                    print(f"  Modifiers ({len(sample.item.all_modifiers)}):")
                    for i, mod in enumerate(sample.item.all_modifiers[:5]):  # Show first 5
                        print(f"    {i+1}. {mod.text}")
                    if len(sample.item.all_modifiers) > 5:
                        print(f"    ... and {len(sample.item.all_modifiers) - 5} more")
                else:
                    print("  ❌ No modifiers found")
            else:
                print("❌ No listings found")
        else:
            print(f"❌ No data returned for {test_tablet}")
        
        return len(listings) > 0 if test_tablet in tablet_data else False
        
    except Exception as e:
        print(f"❌ Data collection failed: {e}")
        import traceback
        print("Full traceback:")
        traceback.print_exc()
        return False

def test_all_tablets():
    """Test data collection for all tablet types."""
    print("\n\n🎯 Testing All Tablet Types")
    print("=" * 50)
    
    try:
        config = load_config()
        client = TradeAPIClient(
            poesessid=config.api.poesessid,
            user_agent=config.api.user_agent
        )
        collector = MarketDataCollector(client)
        
        league = "Rise of the Abyssal"
        
        # Collect data for all tablets
        tablet_data = collector.collect_precursor_tablet_data(
            league=league,
            tablet_types=TABLET_TYPES,
            max_results=5  # Very small sample
        )
        
        total_listings = 0
        for tablet_type, listings in tablet_data.items():
            count = len(listings)
            total_listings += count
            status = "✅" if count > 0 else "❌"
            print(f"{status} {tablet_type}: {count} listings")
        
        print(f"\nTotal listings across all tablets: {total_listings}")
        return total_listings > 0
        
    except Exception as e:
        print(f"❌ Failed to test all tablets: {e}")
        return False

if __name__ == "__main__":
    print("🧪 PoE 2 Trade API Test Suite")
    print("=" * 60)
    
    # Run tests
    api_ok = test_api_connection()
    
    if api_ok:
        data_ok = test_data_collection()
        if data_ok:
            all_tablets_ok = test_all_tablets()
        else:
            print("\n⚠️  Skipping all tablets test due to data collection failure")
    else:
        print("\n⚠️  Skipping data tests due to API connection failure")
    
    print("\n" + "=" * 60)
    print("🏁 Test Complete")
