# PoE 2 Trade Valuation Tool

A Python application for valuing Path of Exile 2 items using the official trade market API, with a focus on precursor tablet modifier valuation.

## Features

- **Market Data Collection**: Fetch real-time market data from the PoE 2 trade API
- **Modifier Valuation**: Analyze and assign values to individual modifiers on precursor tablets
- **Caching System**: Reduce API calls with intelligent caching
- **CLI Interface**: Easy-to-use command-line interface
- **Configurable**: Flexible configuration for different leagues and search parameters

## Use Case

This tool is designed for players who buy and sell precursor tablets and need to quickly identify which modifiers are valuable at any given time. It provides a "source of truth" for modifier values to help with purchasing and selling decisions.

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd PoE-Trade

# Install dependencies
pip install -r requirements.txt

# Set up configuration
cp config/config.example.yaml config/config.yaml
# Edit config.yaml with your POESESSID and preferences
```

## Configuration

You'll need to provide your POESESSID cookie from the Path of Exile website. This can be found in your browser's developer tools when logged into pathofexile.com.

## Usage

### 🌐 Web Interface (Recommended)

The tool provides a beautiful, easy-to-use web interface:

```bash
# Run the web interface
python run_web.py
```

The web interface will automatically open in your browser at `http://127.0.0.1:5000`.

**Features:**
- 🎨 Beautiful, responsive design with PoE-themed styling
- 🔍 Real-time item search with detailed results
- 📊 Market analysis with modifier valuation
- 💎 Specialized precursor tablet analysis
- 🌐 Easy configuration management
- 📱 Mobile-friendly interface

### 💻 Command Line Interface

For advanced users, a command-line interface is also available:

```bash
# Initialize configuration
python -m poe_trade.cli.main init-config

# Test API connection
python -m poe_trade.cli.main test-connection

# Search for items
python -m poe_trade.cli.main search --item-type "Precursor Tablet" --league "Standard"

# List available leagues
python -m poe_trade.cli.main list-leagues
```

## API Reference

This tool uses the official Path of Exile 2 trade API:
- Base URL: `https://www.pathofexile.com/api/trade2/`
- Documentation: https://www.pathofexile.com/developer/docs/reference

## Project Structure

```
poe_trade/
├── api/           # API client and trade interactions
├── models/        # Data models and schemas
├── market/        # Market data collection and analysis
├── storage/       # Data storage and caching
├── cli/           # Command-line interface
├── web/           # Web interface (Flask app)
└── config/        # Configuration management

run_web.py         # Web interface launcher
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
