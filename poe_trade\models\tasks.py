"""Task system models for managing PoE API operations."""

from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field
import uuid
import json


class TaskStatus(str, Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(str, Enum):
    """Task priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class TaskType(str, Enum):
    """Types of tasks that can be executed."""
    PRECURSOR_ANALYSIS = "precursor_analysis"
    ITEM_SEARCH = "item_search"
    MARKET_DATA_COLLECTION = "market_data_collection"
    MODIFIER_ANALYSIS = "modifier_analysis"
    SYNERGY_DETECTION = "synergy_detection"
    API_TEST = "api_test"


class TaskProgress(BaseModel):
    """Task progress tracking."""
    current_step: str = "Initializing"
    total_steps: int = 1
    completed_steps: int = 0
    percentage: float = 0.0
    details: str = ""
    sub_tasks: List[str] = Field(default_factory=list)
    
    @property
    def is_complete(self) -> bool:
        return self.completed_steps >= self.total_steps
    
    def update(self, step: str, details: str = "", increment: bool = True):
        """Update progress with new step information."""
        self.current_step = step
        self.details = details
        if increment and self.completed_steps < self.total_steps:
            self.completed_steps += 1
        self.percentage = (self.completed_steps / self.total_steps) * 100


class TaskResult(BaseModel):
    """Task execution result."""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    warnings: List[str] = Field(default_factory=list)
    execution_time: Optional[float] = None
    api_calls_made: int = 0
    rate_limit_hits: int = 0


class TaskLog(BaseModel):
    """Individual task log entry."""
    timestamp: datetime
    level: str  # INFO, WARNING, ERROR, DEBUG
    message: str
    details: Optional[Dict[str, Any]] = None


class Task(BaseModel):
    """Main task model."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    description: str
    task_type: TaskType
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.NORMAL
    
    # Task configuration
    parameters: Dict[str, Any] = Field(default_factory=dict)
    
    # Execution tracking
    created_at: datetime = Field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    # Progress and results
    progress: TaskProgress = Field(default_factory=TaskProgress)
    result: Optional[TaskResult] = None
    logs: List[TaskLog] = Field(default_factory=list)
    
    # Rate limiting and scheduling
    estimated_api_calls: int = 0
    max_retries: int = 3
    retry_count: int = 0
    next_retry_at: Optional[datetime] = None
    
    # Dependencies and relationships
    depends_on: List[str] = Field(default_factory=list)  # Task IDs this task depends on
    parent_task_id: Optional[str] = None
    child_task_ids: List[str] = Field(default_factory=list)
    
    def add_log(self, level: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Add a log entry to the task."""
        log_entry = TaskLog(
            timestamp=datetime.utcnow(),
            level=level,
            message=message,
            details=details
        )
        self.logs.append(log_entry)
        self.last_updated = datetime.utcnow()
    
    def update_status(self, status: TaskStatus, message: str = ""):
        """Update task status with logging."""
        old_status = self.status
        self.status = status
        self.last_updated = datetime.utcnow()
        
        if status == TaskStatus.RUNNING and not self.started_at:
            self.started_at = datetime.utcnow()
        elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            self.completed_at = datetime.utcnow()
        
        log_message = message or f"Status changed from {old_status} to {status}"
        self.add_log("INFO", log_message)
    
    def update_progress(self, step: str, details: str = "", increment: bool = True):
        """Update task progress."""
        self.progress.update(step, details, increment)
        self.last_updated = datetime.utcnow()
        self.add_log("DEBUG", f"Progress: {step}", {"details": details, "percentage": self.progress.percentage})
    
    def set_result(self, result: TaskResult):
        """Set task result and update status."""
        self.result = result
        self.last_updated = datetime.utcnow()
        
        if result.success:
            self.update_status(TaskStatus.COMPLETED, "Task completed successfully")
        else:
            self.update_status(TaskStatus.FAILED, f"Task failed: {result.error}")
    
    def can_run(self) -> bool:
        """Check if task can be executed now."""
        if self.status != TaskStatus.PENDING:
            return False
        
        # Check retry timing
        if self.next_retry_at and datetime.utcnow() < self.next_retry_at:
            return False
        
        return True
    
    def schedule_retry(self, delay_seconds: int = 60):
        """Schedule task for retry after delay."""
        self.retry_count += 1
        self.next_retry_at = datetime.utcnow() + timedelta(seconds=delay_seconds)
        self.status = TaskStatus.PENDING
        self.add_log("INFO", f"Scheduled retry #{self.retry_count} in {delay_seconds} seconds")
    
    @property
    def execution_time(self) -> Optional[float]:
        """Calculate task execution time in seconds."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()
    
    @property
    def is_terminal(self) -> bool:
        """Check if task is in a terminal state."""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "task_type": self.task_type.value,
            "status": self.status.value,
            "priority": self.priority.value,
            "parameters": self.parameters,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "last_updated": self.last_updated.isoformat(),
            "progress": {
                "current_step": self.progress.current_step,
                "total_steps": self.progress.total_steps,
                "completed_steps": self.progress.completed_steps,
                "percentage": self.progress.percentage,
                "details": self.progress.details
            },
            "result": self.result.dict() if self.result else None,
            "logs": [
                {
                    "timestamp": log.timestamp.isoformat(),
                    "level": log.level,
                    "message": log.message,
                    "details": log.details
                }
                for log in self.logs[-10:]  # Last 10 logs only
            ],
            "estimated_api_calls": self.estimated_api_calls,
            "retry_count": self.retry_count,
            "execution_time": self.execution_time,
            "depends_on": self.depends_on,
            "parent_task_id": self.parent_task_id,
            "child_task_ids": self.child_task_ids
        }


class TaskQueue(BaseModel):
    """Task queue for managing task execution order."""
    tasks: List[Task] = Field(default_factory=list)
    max_concurrent: int = 3
    rate_limit_window: int = 60  # seconds
    max_requests_per_window: int = 45  # PoE API limit
    
    def add_task(self, task: Task):
        """Add task to queue."""
        self.tasks.append(task)
        self.tasks.sort(key=lambda t: (t.priority.value, t.created_at))
    
    def get_next_task(self) -> Optional[Task]:
        """Get next task that can be executed."""
        for task in self.tasks:
            if task.can_run() and task.status == TaskStatus.PENDING:
                return task
        return None
    
    def remove_task(self, task_id: str):
        """Remove task from queue."""
        self.tasks = [t for t in self.tasks if t.id != task_id]
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID."""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None
    
    def get_running_tasks(self) -> List[Task]:
        """Get all currently running tasks."""
        return [t for t in self.tasks if t.status == TaskStatus.RUNNING]
    
    def get_pending_tasks(self) -> List[Task]:
        """Get all pending tasks."""
        return [t for t in self.tasks if t.status == TaskStatus.PENDING]
