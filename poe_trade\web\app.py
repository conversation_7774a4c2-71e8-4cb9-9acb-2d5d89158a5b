"""
Flask web application for the PoE 2 trade valuation tool.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Any
from flask import Flask, render_template, request, jsonify, send_from_directory
from werkzeug.exceptions import HTTPException

from ..config import load_config
from ..api import TradeAPIClient, APIError
from ..market import MarketDataCollector, MarketAnalyzer
from ..models import Currency
from ..data.precursor_modifiers import PRECURSOR_TABLET_DATA, TABLET_TYPES, get_all_modifiers, HIGH_VALUE_FAMILIES
from ..models.tasks import Task, TaskType, TaskPriority, TaskStatus
from ..tasks.manager import TaskManager
from collections import defaultdict, Counter
import uuid
from datetime import datetime


def detect_modifier_synergies(tablet_data, min_confidence=0.5):
    """Detect modifier combinations that are more valuable together."""
    synergies = []

    # Collect items with multiple valuable modifiers
    multi_mod_items = []

    for tablet_type, listings in tablet_data.items():
        for listing in listings:
            if not listing.price_value:
                continue

            valuable_mods = []
            for mod in listing.item.all_modifiers:
                # Simple heuristic: consider modifiers with certain keywords as potentially valuable
                if any(keyword in mod.text.lower() for keyword in [
                    'increased', 'more', 'added', 'damage', 'life', 'mana', 'resistance',
                    'critical', 'attack', 'cast', 'movement', 'area'
                ]):
                    valuable_mods.append(mod.text)

            if len(valuable_mods) >= 2:
                multi_mod_items.append({
                    'modifiers': valuable_mods,
                    'price': listing.price_value,
                    'currency': listing.price_currency or 'chaos'
                })

    # Find common combinations
    if len(multi_mod_items) >= 5:  # Need minimum sample size
        combo_prices = defaultdict(list)

        for item in multi_mod_items:
            # Check all 2-modifier combinations
            mods = item['modifiers']
            for i in range(len(mods)):
                for j in range(i + 1, len(mods)):
                    combo = tuple(sorted([mods[i], mods[j]]))
                    combo_prices[combo].append(item['price'])

        # Identify synergies (combinations that appear frequently and have high prices)
        for combo, prices in combo_prices.items():
            if len(prices) >= 3:  # Minimum occurrences
                avg_price = sum(prices) / len(prices)
                if avg_price >= 10:  # Minimum value threshold
                    synergies.append({
                        'modifiers': list(combo),
                        'bonus_value': avg_price * 0.2,  # Estimate 20% synergy bonus
                        'currency': 'chaos',
                        'occurrences': len(prices)
                    })

    return sorted(synergies, key=lambda x: x['bonus_value'], reverse=True)[:5]


def _modifier_text_matches(item_text: str, expected_text: str) -> bool:
    """Check if an item modifier text matches an expected modifier pattern."""
    import re

    # Normalize both texts
    item_lower = item_text.lower().strip()
    expected_lower = expected_text.lower().strip()

    # Direct substring match first (most reliable)
    if expected_lower in item_lower or item_lower in expected_lower:
        return True

    # Extract key words from expected text (remove common words and values)
    expected_clean = re.sub(r'\(\d+[–-]\d+\)', '', expected_lower)
    expected_clean = re.sub(r'\d+', '', expected_clean)

    # Key words that should match
    key_words = []
    for word in expected_clean.split():
        if word not in {'increased', 'reduced', 'more', 'less', '%', 'your', 'maps', 'in', 'of', 'the', 'to', 'a', 'an', 'contain', 'have', 'are', 'is', 'with', 'for', 'from', 'by'}:
            if len(word) > 2:  # Only meaningful words
                key_words.append(word)

    # Check if key words appear in item text
    if not key_words:
        return False

    matches = 0
    for word in key_words:
        if word in item_lower:
            matches += 1

    # Need at least 60% of key words to match, minimum 1
    required_matches = max(1, int(len(key_words) * 0.6))
    return matches >= required_matches


def _extract_modifier_values(text: str) -> List[float]:
    """Extract numeric values from modifier text."""
    import re
    # Find all numbers in the text
    numbers = re.findall(r'\d+(?:\.\d+)?', text)
    return [float(num) for num in numbers]


def _calculate_modifier_tier(modifier_text: str, known_modifier) -> str:
    """Calculate the tier (Low/Mid/High) of a modifier based on its values."""
    values = _extract_modifier_values(modifier_text)
    if not values or not hasattr(known_modifier, 'value_range'):
        return "Unknown"

    # Get the primary value (usually the first number)
    primary_value = values[0] if values else 0

    # Parse value range from known modifier
    if hasattr(known_modifier, 'value_range') and known_modifier.value_range:
        import re
        range_match = re.search(r'(\d+).*?(\d+)', known_modifier.value_range)
        if range_match:
            min_val, max_val = float(range_match.group(1)), float(range_match.group(2))
            range_size = max_val - min_val

            if primary_value <= min_val + (range_size * 0.33):
                return "Low"
            elif primary_value <= min_val + (range_size * 0.66):
                return "Mid"
            else:
                return "High"

    return "Unknown"


def _calculate_synergy_multiplier(modifiers_on_item: List[dict]) -> float:
    """Calculate synergy multiplier for combinations of modifiers."""
    if len(modifiers_on_item) < 2:
        return 1.0

    # High-value synergies
    synergy_families = {
        'quantity': ['quantity', 'quant'],
        'rarity': ['rarity', 'rare'],
        'pack_size': ['pack size', 'pack'],
        'additional_content': ['additional', 'extra', 'contain'],
        'boss_content': ['boss', 'unique', 'rare monster']
    }

    # Check for synergies
    modifier_families = []
    for mod in modifiers_on_item:
        mod_text = mod['text'].lower()
        for family, keywords in synergy_families.items():
            if any(keyword in mod_text for keyword in keywords):
                modifier_families.append(family)
                break

    # Calculate multiplier based on synergies
    unique_families = set(modifier_families)
    if len(unique_families) >= 3:
        return 2.5  # Very strong synergy
    elif len(unique_families) == 2:
        # Specific high-value combinations
        if 'quantity' in unique_families and 'additional_content' in unique_families:
            return 2.2
        elif 'quantity' in unique_families and 'pack_size' in unique_families:
            return 2.0
        elif 'rarity' in unique_families and 'additional_content' in unique_families:
            return 1.8
        else:
            return 1.5  # General synergy

    return 1.0  # No synergy


def create_app(config_path=None):
    """Create and configure the Flask application."""
    app = Flask(__name__)
    
    # Configure Flask
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['JSON_SORT_KEYS'] = False
    
    # Load PoE trade configuration
    try:
        settings = load_config(config_path)
        app.config['POE_SETTINGS'] = settings
        
        # Create API client
        api_client = TradeAPIClient(
            poesessid=settings.api.poesessid,
            base_url=settings.api.base_url,
            rate_limit=settings.api.rate_limit,
            timeout=settings.api.timeout,
            user_agent=settings.api.user_agent
        )
        app.config['API_CLIENT'] = api_client
        
        # Create market components
        app.config['MARKET_COLLECTOR'] = MarketDataCollector(api_client)
        app.config['MARKET_ANALYZER'] = MarketAnalyzer(
            min_listings=settings.valuation.min_listings,
            confidence_threshold=settings.valuation.min_confidence
        )

        # Create and start task manager
        task_manager = TaskManager(api_client)
        task_manager.start()
        app.config['TASK_MANAGER'] = task_manager
        
    except Exception as e:
        app.logger.error(f"Failed to load configuration: {e}")
        app.config['CONFIG_ERROR'] = str(e)
    
    # Register routes
    register_routes(app)
    register_error_handlers(app)
    
    return app


def register_routes(app):
    """Register application routes."""
    
    @app.route('/')
    def index():
        """Main dashboard page."""
        config_error = app.config.get('CONFIG_ERROR')
        if config_error:
            return render_template('config_error.html', error=config_error)
        
        settings = app.config.get('POE_SETTINGS')
        return render_template('index.html', 
                             leagues=settings.league.available if settings else [],
                             default_league=settings.league.default if settings else 'Rise of the Abyssal')
    
    @app.route('/api/leagues')
    def get_leagues():
        """Get available leagues from the API."""
        try:
            api_client = app.config['API_CLIENT']
            leagues = api_client.get_leagues()
            return jsonify({'success': True, 'leagues': leagues})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/test-connection')
    def test_connection():
        """Test API connection."""
        try:
            api_client = app.config['API_CLIENT']
            leagues = api_client.get_leagues()
            return jsonify({
                'success': True, 
                'message': f'Connected successfully! Found {len(leagues)} leagues.',
                'league_count': len(leagues)
            })
        except APIError as e:
            return jsonify({
                'success': False, 
                'error': f'API Error: {str(e)}',
                'error_type': 'api_error'
            }), 400
        except Exception as e:
            return jsonify({
                'success': False, 
                'error': f'Connection failed: {str(e)}',
                'error_type': 'connection_error'
            }), 500
    
    @app.route('/api/search', methods=['POST'])
    def search_items():
        """Search for items in the trade market."""
        try:
            data = request.get_json()
            league = data.get('league', 'Standard')
            item_type = data.get('item_type', '')
            max_results = min(int(data.get('max_results', 50)), 200)  # Cap at 200
            
            if not item_type:
                return jsonify({'success': False, 'error': 'Item type is required'}), 400
            
            collector = app.config['MARKET_COLLECTOR']
            listings = collector.collect_item_listings(
                item_type=item_type,
                league=league,
                max_results=max_results,
                online_only=True
            )
            
            # Convert listings to JSON-serializable format
            results = []
            for listing in listings:
                result = {
                    'item': {
                        'name': listing.item.name,
                        'type_line': listing.item.type_line,
                        'rarity': listing.item.rarity,
                        'item_level': listing.item.item_level,
                        'modifiers': [
                            {'text': mod.text, 'type': mod.type.value}
                            for mod in listing.item.all_modifiers
                        ]
                    },
                    'price': {
                        'amount': listing.price_value,
                        'currency': listing.price_currency
                    } if listing.price_value else None,
                    'seller': {
                        'name': listing.account.get('name') if listing.account else 'Unknown',
                        'online': listing.is_online
                    }
                }
                results.append(result)
            
            return jsonify({
                'success': True,
                'results': results,
                'count': len(results),
                'search_params': {
                    'league': league,
                    'item_type': item_type,
                    'max_results': max_results
                }
            })
            
        except Exception as e:
            app.logger.error(f"Search error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/analyze', methods=['POST'])
    def analyze_market():
        """Analyze market data for an item type."""
        try:
            data = request.get_json()
            league = data.get('league', 'Standard')
            item_type = data.get('item_type', '')
            max_results = min(int(data.get('max_results', 100)), 500)
            
            if not item_type:
                return jsonify({'success': False, 'error': 'Item type is required'}), 400
            
            # Collect market data
            collector = app.config['MARKET_COLLECTOR']
            listings = collector.collect_item_listings(
                item_type=item_type,
                league=league,
                max_results=max_results,
                online_only=True
            )
            
            if not listings:
                return jsonify({
                    'success': False, 
                    'error': f'No listings found for {item_type} in {league}'
                }), 404
            
            # Analyze the data
            analyzer = app.config['MARKET_ANALYZER']
            market_data = analyzer.analyze_market_data(item_type, league, listings)
            
            # Generate report
            report = analyzer.generate_valuation_report(market_data)
            
            return jsonify({
                'success': True,
                'analysis': report,
                'raw_data': {
                    'total_listings': len(listings),
                    'data_quality': market_data.data_quality,
                    'base_price': {
                        'amount': market_data.base_price.amount,
                        'currency': market_data.base_price.currency.value if hasattr(market_data.base_price.currency, 'value') else market_data.base_price.currency,
                        'confidence': market_data.base_price.confidence
                    } if market_data.base_price else None
                }
            })
            
        except Exception as e:
            app.logger.error(f"Analysis error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500
    
    @app.route('/api/precursor-analysis', methods=['POST'])
    def analyze_precursor_tablets():
        """Specialized analysis for precursor tablets."""
        try:
            data = request.get_json()
            league = data.get('league', 'Standard')
            max_results = min(int(data.get('max_results', 200)), 500)

            collector = app.config['MARKET_COLLECTOR']

            # Collect data for different precursor tablet types
            tablet_data = collector.collect_precursor_tablet_data(
                league=league,
                max_results=max_results
            )

            analyzer = app.config['MARKET_ANALYZER']
            analysis_results = {}

            for tablet_type, listings in tablet_data.items():
                if listings:
                    market_data = analyzer.analyze_market_data(tablet_type, league, listings)
                    report = analyzer.generate_valuation_report(market_data)
                    analysis_results[tablet_type] = report

            return jsonify({
                'success': True,
                'tablet_analysis': analysis_results,
                'summary': {
                    'total_tablet_types': len(analysis_results),
                    'league': league,
                    'max_results_per_type': max_results
                }
            })

        except Exception as e:
            app.logger.error(f"Precursor analysis error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/precursor-modifier-analysis', methods=['POST'])
    def analyze_precursor_modifiers():
        """Comprehensive modifier analysis for precursor tablets using task system."""
        try:
            data = request.get_json()
            league = data.get('league', 'Rise of the Abyssal')
            max_results = min(int(data.get('max_results', 200)), 500)
            min_confidence = float(data.get('min_confidence', 0.5))

            # Create a task for the analysis
            task = Task(
                name=f'Precursor Analysis - {league}',
                description=f'Comprehensive modifier analysis for precursor tablets in {league}',
                task_type=TaskType.PRECURSOR_ANALYSIS,
                priority=TaskPriority.NORMAL,
                parameters={
                    'league': league,
                    'max_results': max_results,
                    'min_confidence': min_confidence
                }
            )

            # Submit task
            task_manager = app.config['TASK_MANAGER']
            task_id = task_manager.submit_task(task)

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': 'Analysis task created successfully',
                'estimated_time': '2-5 minutes'
            })

        except Exception as e:
            app.logger.error(f"Error creating precursor analysis task: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>/result')
    def get_task_result(task_id):
        """Get the result of a completed task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            task = task_manager.get_task(task_id)

            if not task:
                return jsonify({'success': False, 'error': 'Task not found'}), 404

            if not task.is_terminal:
                return jsonify({
                    'success': False,
                    'error': 'Task not completed yet',
                    'status': task.status.value,
                    'progress': task.progress.percentage
                }), 202  # Accepted but not ready

            if task.status == TaskStatus.COMPLETED and task.result:
                return jsonify({
                    'success': True,
                    'task_status': task.status.value,
                    'result': task.result.data
                })
            else:
                return jsonify({
                    'success': False,
                    'task_status': task.status.value,
                    'error': task.result.error if task.result else 'Task failed without result'
                })

        except Exception as e:
            app.logger.error(f"Error getting task result {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500


    @app.route('/api/generate-regex', methods=['POST'])
    def generate_stash_regex():
        """Generate regex patterns for stash searching based on valuable modifiers."""
        try:
            data = request.get_json()
            min_value = float(data.get('min_value', 10))  # Minimum chaos value
            max_patterns = int(data.get('max_patterns', 5))  # Max number of regex patterns

            # Get all modifiers from our database
            all_modifiers = get_all_modifiers()

            # Filter for high-value modifiers
            valuable_mods = []
            for mod_key, mod_info in all_modifiers.items():
                if mod_info.family in HIGH_VALUE_FAMILIES:
                    valuable_mods.append(mod_info)

            # Generate regex patterns
            regex_patterns = []

            # Pattern 1: Quantity modifiers
            quantity_keywords = ["quantity", "quant"]
            quantity_pattern = "|".join(quantity_keywords)
            if len(quantity_pattern) <= 50:
                regex_patterns.append({
                    'name': 'Quantity Modifiers',
                    'pattern': quantity_pattern,
                    'description': 'Finds tablets with quantity modifiers',
                    'length': len(quantity_pattern)
                })

            # Pattern 2: Rarity modifiers
            rarity_keywords = ["rarity", "rare"]
            rarity_pattern = "|".join(rarity_keywords)
            if len(rarity_pattern) <= 50:
                regex_patterns.append({
                    'name': 'Rarity Modifiers',
                    'pattern': rarity_pattern,
                    'description': 'Finds tablets with rarity modifiers',
                    'length': len(rarity_pattern)
                })

            # Pattern 3: Pack size modifiers
            pack_keywords = ["pack size", "pack"]
            pack_pattern = "|".join(pack_keywords)
            if len(pack_pattern) <= 50:
                regex_patterns.append({
                    'name': 'Pack Size Modifiers',
                    'pattern': pack_pattern,
                    'description': 'Finds tablets with pack size modifiers',
                    'length': len(pack_pattern)
                })

            # Pattern 4: Additional content modifiers
            additional_keywords = ["additional", "extra", "contain"]
            additional_pattern = "|".join(additional_keywords)
            if len(additional_pattern) <= 50:
                regex_patterns.append({
                    'name': 'Additional Content',
                    'pattern': additional_pattern,
                    'description': 'Finds tablets with additional content',
                    'length': len(additional_pattern)
                })

            # Pattern 5: Specific high-value terms
            high_value_terms = ["breach", "logbook", "waystone", "boss"]
            high_value_pattern = "|".join(high_value_terms)
            if len(high_value_pattern) <= 50:
                regex_patterns.append({
                    'name': 'High-Value Content',
                    'pattern': high_value_pattern,
                    'description': 'Finds tablets with high-value content types',
                    'length': len(high_value_pattern)
                })

            # Limit to requested number of patterns
            regex_patterns = regex_patterns[:max_patterns]

            return jsonify({
                'success': True,
                'patterns': regex_patterns,
                'total_patterns': len(regex_patterns),
                'min_value_threshold': min_value
            })

        except Exception as e:
            app.logger.error(f"Regex generation error: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/precursor-analysis')
    def precursor_analysis_page():
        """Precursor tablet analysis page."""
        config_error = app.config.get('CONFIG_ERROR')
        if config_error:
            return render_template('config_error.html', error=config_error)

        settings = app.config.get('POE_SETTINGS')
        return render_template('precursor_analysis.html',
                             leagues=settings.league.available if settings else [],
                             default_league=settings.league.default if settings else 'Rise of the Abyssal')

    @app.route('/tasks')
    def tasks_page():
        """Task management page."""
        config_error = app.config.get('CONFIG_ERROR')
        if config_error:
            return render_template('config_error.html', error=config_error)

        return render_template('tasks.html')

    @app.route('/api/tasks')
    def get_tasks():
        """Get all tasks and statistics."""
        try:
            task_manager = app.config['TASK_MANAGER']
            tasks = task_manager.get_all_tasks()
            stats = task_manager.get_stats()

            return jsonify({
                'success': True,
                'tasks': [task.to_dict() for task in tasks],
                'stats': stats
            })
        except Exception as e:
            app.logger.error(f"Error getting tasks: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks', methods=['POST'])
    def create_task():
        """Create a new task."""
        try:
            data = request.get_json()
            task_type = TaskType(data.get('task_type'))

            # Create task
            task = Task(
                name=data.get('name', f'{task_type.value} Task'),
                description=data.get('description', f'Automated {task_type.value} task'),
                task_type=task_type,
                priority=TaskPriority(data.get('priority', 'normal')),
                parameters=data.get('parameters', {})
            )

            # Estimate API calls based on task type
            if task_type == TaskType.PRECURSOR_ANALYSIS:
                task.estimated_api_calls = len(TABLET_TYPES) * 2  # 2 calls per tablet type
            elif task_type == TaskType.ITEM_SEARCH:
                task.estimated_api_calls = 2
            elif task_type == TaskType.API_TEST:
                task.estimated_api_calls = 2
            else:
                task.estimated_api_calls = 1

            # Submit task
            task_manager = app.config['TASK_MANAGER']
            task_id = task_manager.submit_task(task)

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': 'Task created successfully'
            })

        except Exception as e:
            app.logger.error(f"Error creating task: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>')
    def get_task(task_id):
        """Get a specific task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            task = task_manager.get_task(task_id)

            if not task:
                return jsonify({'success': False, 'error': 'Task not found'}), 404

            return jsonify({
                'success': True,
                'task': task.to_dict()
            })
        except Exception as e:
            app.logger.error(f"Error getting task {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>/pause', methods=['POST'])
    def pause_task(task_id):
        """Pause a running task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            success = task_manager.pause_task(task_id)

            if success:
                return jsonify({'success': True, 'message': 'Task paused'})
            else:
                return jsonify({'success': False, 'error': 'Cannot pause task'}), 400

        except Exception as e:
            app.logger.error(f"Error pausing task {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>/resume', methods=['POST'])
    def resume_task(task_id):
        """Resume a paused task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            success = task_manager.resume_task(task_id)

            if success:
                return jsonify({'success': True, 'message': 'Task resumed'})
            else:
                return jsonify({'success': False, 'error': 'Cannot resume task'}), 400

        except Exception as e:
            app.logger.error(f"Error resuming task {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>/cancel', methods=['POST'])
    def cancel_task(task_id):
        """Cancel a task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            success = task_manager.cancel_task(task_id)

            if success:
                return jsonify({'success': True, 'message': 'Task cancelled'})
            else:
                return jsonify({'success': False, 'error': 'Cannot cancel task'}), 400

        except Exception as e:
            app.logger.error(f"Error cancelling task {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/tasks/<task_id>/restart', methods=['POST'])
    def restart_task(task_id):
        """Restart a failed or cancelled task."""
        try:
            task_manager = app.config['TASK_MANAGER']
            success = task_manager.restart_task(task_id)

            if success:
                return jsonify({'success': True, 'message': 'Task restarted'})
            else:
                return jsonify({'success': False, 'error': 'Cannot restart task'}), 400

        except Exception as e:
            app.logger.error(f"Error restarting task {task_id}: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/config')
    def config_page():
        """Configuration management page."""
        config_error = app.config.get('CONFIG_ERROR')
        if config_error:
            return render_template('config_error.html', error=config_error)

        return render_template('config.html')

    @app.route('/api/config')
    def get_config():
        """Get current configuration."""
        try:
            settings = app.config.get('POE_SETTINGS')
            if not settings:
                return jsonify({'success': False, 'error': 'No configuration loaded'}), 500

            # Convert settings to dictionary (assuming it has a to_dict method or similar)
            config_dict = {
                'api': {
                    'poesessid': '***HIDDEN***',  # Don't expose the actual session ID
                    'base_url': settings.api.base_url,
                    'user_agent': settings.api.user_agent,
                    'rate_limit': settings.api.rate_limit,
                    'timeout': settings.api.timeout
                },
                'league': {
                    'default': settings.league.default,
                    'available': settings.league.available
                },
                'valuation': {
                    'min_listings': settings.valuation.min_listings,
                    'min_confidence': settings.valuation.min_confidence,
                    'max_results': getattr(settings.valuation, 'max_results', 200),
                    'chaos_to_exalt_rate': getattr(settings.valuation, 'chaos_to_exalt_rate', 180),
                    'outlier_threshold': getattr(settings.valuation, 'outlier_threshold', 3.0)
                },
                'tasks': {
                    'max_concurrent': getattr(settings, 'max_concurrent_tasks', 2),
                    'max_retries': getattr(settings, 'max_retries', 3),
                    'timeout_minutes': getattr(settings, 'timeout_minutes', 10)
                }
            }

            return jsonify({'success': True, 'config': config_dict})

        except Exception as e:
            app.logger.error(f"Error getting configuration: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/config', methods=['POST'])
    def save_config():
        """Save configuration changes."""
        try:
            data = request.get_json()
            config_data = data.get('config', {})

            # Load current config file
            config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config.yaml')

            # Update configuration
            # This is a simplified version - you'd want more robust config handling
            updated_config = {
                'api': {
                    'poesessid': config_data.get('api', {}).get('poesessid', ''),
                    'base_url': config_data.get('api', {}).get('base_url', 'https://www.pathofexile.com/api/trade2'),
                    'user_agent': config_data.get('api', {}).get('user_agent', 'PoE2-Trade-Tool/1.0'),
                    'rate_limit': int(config_data.get('api', {}).get('rate_limit', 45)),
                    'timeout': int(config_data.get('api', {}).get('timeout', 30))
                },
                'league': {
                    'default': config_data.get('league', {}).get('default', 'Rise of the Abyssal'),
                    'available': config_data.get('league', {}).get('available', ['Rise of the Abyssal', 'Standard', 'Hardcore'])
                },
                'valuation': {
                    'min_listings': int(config_data.get('valuation', {}).get('min_listings', 5)),
                    'min_confidence': float(config_data.get('valuation', {}).get('min_confidence', 0.5)),
                    'max_results': int(config_data.get('valuation', {}).get('max_results', 200)),
                    'chaos_to_exalt_rate': int(config_data.get('valuation', {}).get('chaos_to_exalt_rate', 180)),
                    'outlier_threshold': float(config_data.get('valuation', {}).get('outlier_threshold', 3.0))
                }
            }

            # Save to file
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(updated_config, f, default_flow_style=False)

            return jsonify({
                'success': True,
                'message': 'Configuration saved successfully. Please restart the application to apply changes.'
            })

        except Exception as e:
            app.logger.error(f"Error saving configuration: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/config/test', methods=['POST'])
    def test_config():
        """Test configuration settings."""
        try:
            data = request.get_json()
            test_type = data.get('test_type', 'full')

            results = {}

            if test_type in ['api', 'full']:
                # Test API connection
                try:
                    api_client = app.config['API_CLIENT']
                    leagues = api_client.get_leagues()
                    results['api'] = {
                        'success': True,
                        'message': f'API connection successful. Found {len(leagues)} leagues.',
                        'leagues': leagues[:5]  # First 5 leagues
                    }
                except Exception as e:
                    results['api'] = {
                        'success': False,
                        'message': f'API connection failed: {str(e)}'
                    }

            if test_type in ['search', 'full']:
                # Test search functionality
                try:
                    # Create a test task for search
                    task_manager = app.config['TASK_MANAGER']
                    test_task = Task(
                        name='Configuration Test - API Test',
                        description='Testing API connectivity and search functionality',
                        task_type=TaskType.API_TEST,
                        priority=TaskPriority.HIGH,
                        parameters={}
                    )

                    task_id = task_manager.submit_task(test_task)
                    results['search'] = {
                        'success': True,
                        'message': f'Search test task created: {task_id}',
                        'task_id': task_id
                    }
                except Exception as e:
                    results['search'] = {
                        'success': False,
                        'message': f'Search test failed: {str(e)}'
                    }

            if test_type in ['tasks', 'full']:
                # Test task system
                try:
                    task_manager = app.config['TASK_MANAGER']
                    stats = task_manager.get_stats()
                    results['tasks'] = {
                        'success': True,
                        'message': 'Task system is operational',
                        'stats': stats
                    }
                except Exception as e:
                    results['tasks'] = {
                        'success': False,
                        'message': f'Task system test failed: {str(e)}'
                    }

            return jsonify({'success': True, 'results': results})

        except Exception as e:
            app.logger.error(f"Error testing configuration: {e}")
            return jsonify({'success': False, 'error': str(e)}), 500

    # Individual test endpoints
    @app.route('/api/config/test/api', methods=['POST'])
    def test_api_connection():
        """Test API connection and authentication."""
        try:
            from ..api.client import PoETradeClient

            config = load_config()
            client = PoETradeClient(
                poesessid=config['api']['poesessid'],
                user_agent=config['api']['user_agent']
            )

            # Test with a simple search
            test_result = client.search_items(
                league="Rise of the Abyssal",
                item_name="Precursor Tablet",
                max_results=1
            )

            if test_result and len(test_result) >= 0:  # Even 0 results is a successful connection
                return jsonify({
                    'success': True,
                    'message': f'API connection successful. Test returned {len(test_result)} results.',
                    'details': {
                        'league': "Rise of the Abyssal",
                        'results_count': len(test_result)
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'API connection failed - no response received'
                })

        except Exception as e:
            app.logger.error(f"API test error: {e}")
            return jsonify({
                'success': False,
                'error': f'API test failed: {str(e)}'
            })

    @app.route('/api/config/test/search', methods=['POST'])
    def test_search_functionality():
        """Test search functionality with a known item."""
        try:
            # Create a simple search task
            task_id = task_manager.create_task(
                task_type=TaskType.ITEM_SEARCH,
                name="Search Test",
                description="Testing search functionality",
                priority=TaskPriority.HIGH,
                parameters={
                    'league': 'Rise of the Abyssal',
                    'item_name': 'Precursor Tablet',
                    'max_results': 5
                }
            )

            return jsonify({
                'success': True,
                'message': 'Search test task created successfully',
                'task_id': task_id,
                'details': {
                    'task_type': 'item_search',
                    'estimated_time': '30-60 seconds'
                }
            })

        except Exception as e:
            app.logger.error(f"Search test error: {e}")
            return jsonify({
                'success': False,
                'error': f'Search test failed: {str(e)}'
            })

    @app.route('/api/config/test/tasks', methods=['POST'])
    def test_task_system():
        """Test task system functionality."""
        try:
            # Check if task manager is running
            if not task_manager.is_running():
                return jsonify({
                    'success': False,
                    'error': 'Task manager is not running'
                })

            # Create a test task
            task_id = task_manager.create_task(
                task_type=TaskType.API_TEST,
                name="Task System Test",
                description="Testing task system functionality",
                priority=TaskPriority.NORMAL,
                parameters={'test_type': 'system_check'}
            )

            # Get task status
            task = task_manager.get_task(task_id)

            return jsonify({
                'success': True,
                'message': 'Task system test passed',
                'task_id': task_id,
                'details': {
                    'task_status': task.status.value if task else 'unknown',
                    'queue_size': len(task_manager.task_queue.queue),
                    'manager_running': task_manager.is_running()
                }
            })

        except Exception as e:
            app.logger.error(f"Task system test error: {e}")
            return jsonify({
                'success': False,
                'error': f'Task system test failed: {str(e)}'
            })


def register_error_handlers(app):
    """Register error handlers."""
    
    @app.errorhandler(404)
    def not_found(error):
        return render_template('error.html', 
                             error_code=404, 
                             error_message="Page not found"), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('error.html', 
                             error_code=500, 
                             error_message="Internal server error"), 500
    
    @app.errorhandler(HTTPException)
    def handle_exception(e):
        return render_template('error.html', 
                             error_code=e.code, 
                             error_message=e.description), e.code


if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='127.0.0.1', port=5000)
