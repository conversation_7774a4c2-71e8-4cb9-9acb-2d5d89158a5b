"""
Custom exceptions for the PoE 2 trade API client.
"""


class APIError(Exception):
    """Base exception for API-related errors."""
    
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class RateLimitError(APIError):
    """Raised when API rate limit is exceeded."""
    
    def __init__(self, message: str = "API rate limit exceeded", retry_after: int = None):
        super().__init__(message, status_code=429)
        self.retry_after = retry_after


class AuthenticationError(APIError):
    """Raised when authentication fails."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, status_code=401)


class InvalidRequestError(APIError):
    """Raised when the request is invalid."""
    
    def __init__(self, message: str = "Invalid request", status_code: int = 400):
        super().__init__(message, status_code=status_code)


class ServerError(APIError):
    """Raised when the server returns an error."""
    
    def __init__(self, message: str = "Server error", status_code: int = 500):
        super().__init__(message, status_code=status_code)


class NetworkError(APIError):
    """Raised when network-related errors occur."""
    
    def __init__(self, message: str = "Network error"):
        super().__init__(message)
