{% extends "base.html" %}

{% block title %}Task Manager - PoE 2 Trade Valuation{% endblock %}

{% block extra_css %}
<style>
    .task-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .task-card.status-running {
        border-left-color: #ffc107;
        background: linear-gradient(90deg, rgba(255, 193, 7, 0.1) 0%, transparent 100%);
    }
    
    .task-card.status-completed {
        border-left-color: #198754;
        background: linear-gradient(90deg, rgba(25, 135, 84, 0.1) 0%, transparent 100%);
    }
    
    .task-card.status-failed {
        border-left-color: #dc3545;
        background: linear-gradient(90deg, rgba(220, 53, 69, 0.1) 0%, transparent 100%);
    }
    
    .task-card.status-pending {
        border-left-color: #6c757d;
    }
    
    .task-card.status-paused {
        border-left-color: #fd7e14;
    }
    
    .task-card.status-cancelled {
        border-left-color: #6f42c1;
    }
    
    .progress-ring {
        width: 40px;
        height: 40px;
    }
    
    .log-entry {
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        border-left: 3px solid transparent;
        padding-left: 8px;
        margin-bottom: 4px;
    }
    
    .log-entry.level-info { border-left-color: #17a2b8; }
    .log-entry.level-warning { border-left-color: #ffc107; }
    .log-entry.level-error { border-left-color: #dc3545; }
    .log-entry.level-debug { border-left-color: #6c757d; }
    
    .stats-card {
        background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, rgba(212, 175, 55, 0.05) 100%);
        border: 1px solid rgba(212, 175, 55, 0.3);
    }
    
    .auto-refresh {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Task Manager Header -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bi bi-gear-wide-connected"></i> Task Manager
                </h4>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success btn-sm" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                        <label class="form-check-label" for="autoRefresh">Auto-refresh</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="col-12 mb-4">
        <div class="row" id="statsCards">
            <!-- Stats will be populated here -->
        </div>
    </div>
    
    <!-- Task Controls -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-plus-circle"></i> Create New Task</h5>
            </div>
            <div class="card-body">
                <form id="createTaskForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="taskType" class="form-label">Task Type</label>
                        <select class="form-select" id="taskType" required>
                            <option value="">Select task type...</option>
                            <option value="precursor_analysis">Precursor Analysis</option>
                            <option value="item_search">Item Search</option>
                            <option value="market_data_collection">Market Data Collection</option>
                            <option value="api_test">API Test</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="taskName" class="form-label">Task Name</label>
                        <input type="text" class="form-control" id="taskName" placeholder="Enter task name" required>
                    </div>
                    <div class="col-md-3">
                        <label for="taskPriority" class="form-label">Priority</label>
                        <select class="form-select" id="taskPriority">
                            <option value="normal">Normal</option>
                            <option value="high">High</option>
                            <option value="low">Low</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="bi bi-plus"></i> Create Task
                        </button>
                    </div>
                </form>
                
                <!-- Task Parameters (Dynamic based on task type) -->
                <div id="taskParameters" class="mt-3" style="display: none;">
                    <!-- Parameters will be populated based on task type -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Task Filters -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">Filter by Status</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">All Tasks</option>
                            <option value="running">Running</option>
                            <option value="pending">Pending</option>
                            <option value="completed">Completed</option>
                            <option value="failed">Failed</option>
                            <option value="paused">Paused</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="typeFilter" class="form-label">Filter by Type</label>
                        <select class="form-select" id="typeFilter">
                            <option value="">All Types</option>
                            <option value="precursor_analysis">Precursor Analysis</option>
                            <option value="item_search">Item Search</option>
                            <option value="market_data_collection">Market Data</option>
                            <option value="api_test">API Test</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sortBy" class="form-label">Sort By</label>
                        <select class="form-select" id="sortBy">
                            <option value="created_at">Created Date</option>
                            <option value="priority">Priority</option>
                            <option value="status">Status</option>
                            <option value="execution_time">Execution Time</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button class="btn btn-outline-danger btn-sm me-2" id="clearCompletedBtn">
                            <i class="bi bi-trash"></i> Clear Completed
                        </button>
                        <button class="btn btn-outline-warning btn-sm" id="pauseAllBtn">
                            <i class="bi bi-pause"></i> Pause All
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Task List -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-task"></i> Tasks
                    <span class="badge bg-secondary ms-2" id="taskCount">0</span>
                </h5>
            </div>
            <div class="card-body">
                <div id="taskList">
                    <!-- Tasks will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Task Details Modal -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark border-warning">
            <div class="modal-header border-warning">
                <h5 class="modal-title text-warning">Task Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="taskDetailsContent">
                    <!-- Task details will be populated here -->
                </div>
            </div>
            <div class="modal-footer border-warning">
                <div id="taskActions">
                    <!-- Task action buttons will be populated here -->
                </div>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let autoRefreshInterval;
    let currentTasks = [];
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadTasks();
        setupEventListeners();
        startAutoRefresh();
    });
    
    function setupEventListeners() {
        // Refresh button
        document.getElementById('refreshBtn').addEventListener('click', loadTasks);
        
        // Auto-refresh toggle
        document.getElementById('autoRefresh').addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
        
        // Task creation form
        document.getElementById('createTaskForm').addEventListener('submit', handleCreateTask);
        document.getElementById('taskType').addEventListener('change', updateTaskParameters);
        
        // Filters
        document.getElementById('statusFilter').addEventListener('change', filterTasks);
        document.getElementById('typeFilter').addEventListener('change', filterTasks);
        document.getElementById('sortBy').addEventListener('change', filterTasks);
        
        // Bulk actions
        document.getElementById('clearCompletedBtn').addEventListener('click', clearCompletedTasks);
        document.getElementById('pauseAllBtn').addEventListener('click', pauseAllTasks);
    }
    
    function startAutoRefresh() {
        stopAutoRefresh();
        autoRefreshInterval = setInterval(loadTasks, 3000); // Refresh every 3 seconds
        document.getElementById('refreshBtn').classList.add('auto-refresh');
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
        document.getElementById('refreshBtn').classList.remove('auto-refresh');
    }
    
    async function loadTasks() {
        try {
            const response = await fetch('/api/tasks');
            const data = await response.json();
            
            if (data.success) {
                currentTasks = data.tasks;
                displayTasks(currentTasks);
                displayStats(data.stats);
            } else {
                showToast('Failed to load tasks: ' + data.error, 'error');
            }
        } catch (error) {
            showToast('Error loading tasks: ' + error.message, 'error');
        }
    }
    
    function displayStats(stats) {
        const statsContainer = document.getElementById('statsCards');
        
        statsContainer.innerHTML = `
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-warning mb-1">${stats.running_tasks}</h4>
                        <small class="text-muted">Running</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-info mb-1">${stats.pending_tasks}</h4>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-success mb-1">${stats.completed_tasks}</h4>
                        <small class="text-muted">Completed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-danger mb-1">${stats.failed_tasks}</h4>
                        <small class="text-muted">Failed</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-primary mb-1">${stats.api_calls_made}</h4>
                        <small class="text-muted">API Calls</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card stats-card">
                    <div class="card-body text-center p-3">
                        <h4 class="text-secondary mb-1">${stats.current_rate_limit}</h4>
                        <small class="text-muted">Rate Limit</small>
                    </div>
                </div>
            </div>
        `;
    }
    
    function displayTasks(tasks) {
        const taskList = document.getElementById('taskList');
        const taskCount = document.getElementById('taskCount');

        taskCount.textContent = tasks.length;

        if (tasks.length === 0) {
            taskList.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h5 class="text-muted mt-3">No tasks found</h5>
                    <p class="text-muted">Create a new task to get started</p>
                </div>
            `;
            return;
        }

        let html = '';
        tasks.forEach(task => {
            const statusBadge = getStatusBadge(task.status);
            const priorityBadge = getPriorityBadge(task.priority);
            const progressBar = getProgressBar(task.progress);
            const executionTime = task.execution_time ? `${task.execution_time.toFixed(1)}s` : '-';

            html += `
                <div class="card task-card status-${task.status} mb-3" data-task-id="${task.id}">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        ${progressBar}
                                    </div>
                                    <div>
                                        <h6 class="mb-1">${task.name}</h6>
                                        <small class="text-muted">${task.description}</small>
                                        <div class="mt-1">
                                            ${statusBadge}
                                            ${priorityBadge}
                                            <span class="badge bg-dark">${task.task_type}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="small">
                                    <div><strong>Created:</strong> ${formatDateTime(task.created_at)}</div>
                                    <div><strong>Duration:</strong> ${executionTime}</div>
                                    <div><strong>Progress:</strong> ${task.progress.current_step}</div>
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-outline-info" onclick="showTaskDetails('${task.id}')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    ${getTaskActionButtons(task)}
                                </div>
                            </div>
                        </div>

                        <!-- Progress Details -->
                        ${task.status === 'running' ? `
                            <div class="mt-3">
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-warning" style="width: ${task.progress.percentage}%"></div>
                                </div>
                                <small class="text-muted">${task.progress.details}</small>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        taskList.innerHTML = html;
    }

    function getStatusBadge(status) {
        const badges = {
            'pending': '<span class="badge bg-secondary">Pending</span>',
            'running': '<span class="badge bg-warning">Running</span>',
            'completed': '<span class="badge bg-success">Completed</span>',
            'failed': '<span class="badge bg-danger">Failed</span>',
            'paused': '<span class="badge bg-warning">Paused</span>',
            'cancelled': '<span class="badge bg-secondary">Cancelled</span>'
        };
        return badges[status] || '<span class="badge bg-dark">Unknown</span>';
    }

    function getPriorityBadge(priority) {
        const badges = {
            'urgent': '<span class="badge bg-danger">Urgent</span>',
            'high': '<span class="badge bg-warning">High</span>',
            'normal': '<span class="badge bg-info">Normal</span>',
            'low': '<span class="badge bg-secondary">Low</span>'
        };
        return badges[priority] || '<span class="badge bg-info">Normal</span>';
    }

    function getProgressBar(progress) {
        const percentage = Math.round(progress.percentage);
        const color = percentage === 100 ? 'success' : 'warning';

        return `
            <div class="progress-ring position-relative">
                <svg class="progress-ring" width="40" height="40">
                    <circle cx="20" cy="20" r="15" fill="transparent" stroke="#6c757d" stroke-width="2"/>
                    <circle cx="20" cy="20" r="15" fill="transparent" stroke="currentColor"
                            stroke-width="2" stroke-dasharray="${94.2}"
                            stroke-dashoffset="${94.2 - (94.2 * percentage) / 100}"
                            class="text-${color}" transform="rotate(-90 20 20)"/>
                </svg>
                <div class="position-absolute top-50 start-50 translate-middle">
                    <small class="fw-bold">${percentage}%</small>
                </div>
            </div>
        `;
    }

    function getTaskActionButtons(task) {
        let buttons = '';

        if (task.status === 'running') {
            buttons += `<button class="btn btn-outline-warning" onclick="pauseTask('${task.id}')">
                <i class="bi bi-pause"></i>
            </button>`;
        }

        if (task.status === 'paused') {
            buttons += `<button class="btn btn-outline-success" onclick="resumeTask('${task.id}')">
                <i class="bi bi-play"></i>
            </button>`;
        }

        if (task.status === 'failed' || task.status === 'cancelled') {
            buttons += `<button class="btn btn-outline-primary" onclick="restartTask('${task.id}')">
                <i class="bi bi-arrow-clockwise"></i>
            </button>`;
        }

        if (!['completed', 'cancelled'].includes(task.status)) {
            buttons += `<button class="btn btn-outline-danger" onclick="cancelTask('${task.id}')">
                <i class="bi bi-x"></i>
            </button>`;
        }

        return buttons;
    }

    function formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    }

    // Task action functions
    async function pauseTask(taskId) {
        await performTaskAction(taskId, 'pause');
    }

    async function resumeTask(taskId) {
        await performTaskAction(taskId, 'resume');
    }

    async function cancelTask(taskId) {
        if (confirm('Are you sure you want to cancel this task?')) {
            await performTaskAction(taskId, 'cancel');
        }
    }

    async function restartTask(taskId) {
        await performTaskAction(taskId, 'restart');
    }

    async function performTaskAction(taskId, action) {
        try {
            const response = await fetch(`/api/tasks/${taskId}/${action}`, {
                method: 'POST'
            });
            const data = await response.json();

            if (data.success) {
                showToast(`Task ${action}ed successfully`, 'success');
                loadTasks(); // Refresh task list
            } else {
                showToast(`Failed to ${action} task: ${data.error}`, 'error');
            }
        } catch (error) {
            showToast(`Error ${action}ing task: ${error.message}`, 'error');
        }
    }

    // Toast notification function
    function showToast(message, type = 'info') {
        // Implementation depends on your toast system
        console.log(`${type.toUpperCase()}: ${message}`);
    }
</script>
{% endblock %}
