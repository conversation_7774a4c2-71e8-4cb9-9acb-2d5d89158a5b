"""
Rate limiting functionality for the PoE 2 trade API client.
"""

import time
import asyncio
from typing import Optional
from collections import deque
from datetime import datetime, timedelta


class RateLimiter:
    """Rate limiter to prevent exceeding API limits."""
    
    def __init__(self, max_requests: int = 60, time_window: int = 60):
        """
        Initialize rate limiter.
        
        Args:
            max_requests: Maximum number of requests allowed
            time_window: Time window in seconds
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a request."""
        async with self._lock:
            now = time.time()
            
            # Remove old requests outside the time window
            while self.requests and self.requests[0] <= now - self.time_window:
                self.requests.popleft()
            
            # If we're at the limit, wait until we can make another request
            if len(self.requests) >= self.max_requests:
                sleep_time = self.requests[0] + self.time_window - now + 0.1  # Small buffer
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    return await self.acquire()  # Recursive call after waiting
            
            # Record this request
            self.requests.append(now)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request without waiting."""
        now = time.time()
        
        # Remove old requests
        while self.requests and self.requests[0] <= now - self.time_window:
            self.requests.popleft()
        
        return len(self.requests) < self.max_requests
    
    def time_until_next_request(self) -> float:
        """Get time in seconds until next request can be made."""
        if self.can_make_request():
            return 0.0
        
        now = time.time()
        return self.requests[0] + self.time_window - now
    
    def reset(self) -> None:
        """Reset the rate limiter."""
        self.requests.clear()


class SyncRateLimiter:
    """Synchronous version of the rate limiter."""
    
    def __init__(self, max_requests: int = 60, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()
    
    def acquire(self) -> None:
        """Acquire permission to make a request."""
        now = time.time()
        
        # Remove old requests outside the time window
        while self.requests and self.requests[0] <= now - self.time_window:
            self.requests.popleft()
        
        # If we're at the limit, wait until we can make another request
        if len(self.requests) >= self.max_requests:
            sleep_time = self.requests[0] + self.time_window - now + 0.1
            if sleep_time > 0:
                time.sleep(sleep_time)
                return self.acquire()  # Recursive call after waiting
        
        # Record this request
        self.requests.append(now)
    
    def can_make_request(self) -> bool:
        """Check if we can make a request without waiting."""
        now = time.time()
        
        # Remove old requests
        while self.requests and self.requests[0] <= now - self.time_window:
            self.requests.popleft()
        
        return len(self.requests) < self.max_requests
