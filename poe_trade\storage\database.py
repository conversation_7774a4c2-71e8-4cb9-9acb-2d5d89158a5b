"""
Database manager for persistent storage of historical market data.
"""

import sqlite3
import logging
import json
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager
import threading

from .models import (
    MarketSnapshot, ModifierTrend, PriceTrend, TrendAnalysis,
    HistoricalMarketData, TrendDirection, TimeFrame
)
from ..models import Currency


logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages SQLite database for historical market data storage."""
    
    def __init__(self, db_path: Union[str, Path], wal_mode: bool = True):
        """
        Initialize database manager.
        
        Args:
            db_path: Path to SQLite database file
            wal_mode: Enable WAL mode for better performance
        """
        self.db_path = Path(db_path)
        self.wal_mode = wal_mode
        self._local = threading.local()
        
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Initialize database schema
        self._initialize_database()
        
        logger.info(f"Database manager initialized: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get thread-local database connection."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                str(self.db_path),
                check_same_thread=False,
                timeout=30.0
            )
            self._local.connection.row_factory = sqlite3.Row
            
            if self.wal_mode:
                self._local.connection.execute("PRAGMA journal_mode=WAL")
            
            # Performance optimizations
            self._local.connection.execute("PRAGMA synchronous=NORMAL")
            self._local.connection.execute("PRAGMA cache_size=10000")
            self._local.connection.execute("PRAGMA temp_store=MEMORY")
        
        return self._local.connection
    
    @contextmanager
    def get_cursor(self):
        """Get database cursor with automatic transaction management."""
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception:
            conn.rollback()
            raise
        finally:
            cursor.close()
    
    def _initialize_database(self):
        """Initialize database schema."""
        with self.get_cursor() as cursor:
            # Market snapshots table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS market_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME NOT NULL,
                    item_type TEXT NOT NULL,
                    league TEXT NOT NULL,
                    base_price REAL,
                    currency TEXT,
                    median_price REAL,
                    min_price REAL,
                    max_price REAL,
                    total_listings INTEGER DEFAULT 0,
                    active_listings INTEGER DEFAULT 0,
                    average_listing_age_hours REAL,
                    data_quality_score REAL DEFAULT 0.0,
                    sample_size INTEGER DEFAULT 0,
                    confidence_score REAL DEFAULT 0.0,
                    price_volatility REAL,
                    market_activity_score REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Modifier trends table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS modifier_trends (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    modifier_id TEXT NOT NULL,
                    modifier_text TEXT NOT NULL,
                    item_type TEXT NOT NULL,
                    league TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    base_value REAL,
                    currency TEXT,
                    value_per_tier REAL,
                    value_per_point REAL,
                    min_value REAL,
                    max_value REAL,
                    median_value REAL,
                    total_listings INTEGER DEFAULT 0,
                    sample_size INTEGER DEFAULT 0,
                    confidence_score REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Price trends table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS price_trends (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_type TEXT NOT NULL,
                    league TEXT NOT NULL,
                    modifier_id TEXT,
                    start_time DATETIME NOT NULL,
                    end_time DATETIME NOT NULL,
                    time_frame TEXT NOT NULL,
                    direction TEXT NOT NULL,
                    price_change_percent REAL NOT NULL,
                    price_change_absolute REAL NOT NULL,
                    currency TEXT NOT NULL,
                    start_price REAL NOT NULL,
                    end_price REAL NOT NULL,
                    min_price REAL NOT NULL,
                    max_price REAL NOT NULL,
                    average_price REAL NOT NULL,
                    median_price REAL NOT NULL,
                    price_volatility REAL NOT NULL,
                    max_drawdown REAL NOT NULL,
                    max_gain REAL NOT NULL,
                    data_points INTEGER NOT NULL,
                    confidence_score REAL DEFAULT 0.0,
                    average_listings REAL DEFAULT 0.0,
                    listing_velocity REAL DEFAULT 0.0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Trend analyses table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS trend_analyses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_type TEXT NOT NULL,
                    league TEXT NOT NULL,
                    modifier_id TEXT,
                    analysis_timestamp DATETIME NOT NULL,
                    overall_direction TEXT NOT NULL,
                    trend_strength REAL DEFAULT 0.0,
                    trend_consistency REAL DEFAULT 0.0,
                    is_trending BOOLEAN DEFAULT FALSE,
                    momentum_score REAL DEFAULT 0.0,
                    support_level REAL,
                    resistance_level REAL,
                    buy_signal_strength REAL DEFAULT 0.0,
                    sell_signal_strength REAL DEFAULT 0.0,
                    hold_recommendation BOOLEAN DEFAULT FALSE,
                    analysis_confidence REAL DEFAULT 0.0,
                    data_coverage_days INTEGER DEFAULT 0,
                    hourly_trend_id INTEGER,
                    daily_trend_id INTEGER,
                    weekly_trend_id INTEGER,
                    monthly_trend_id INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (hourly_trend_id) REFERENCES price_trends (id),
                    FOREIGN KEY (daily_trend_id) REFERENCES price_trends (id),
                    FOREIGN KEY (weekly_trend_id) REFERENCES price_trends (id),
                    FOREIGN KEY (monthly_trend_id) REFERENCES price_trends (id)
                )
            """)
            
            # Create indexes for better query performance
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_snapshots_item_league_time ON market_snapshots (item_type, league, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_snapshots_timestamp ON market_snapshots (timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_modifier_trends_item_league_time ON modifier_trends (item_type, league, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_modifier_trends_modifier_time ON modifier_trends (modifier_id, timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_price_trends_item_league_time ON price_trends (item_type, league, start_time, end_time)",
                "CREATE INDEX IF NOT EXISTS idx_price_trends_timeframe ON price_trends (time_frame, start_time)",
                "CREATE INDEX IF NOT EXISTS idx_trend_analyses_item_league ON trend_analyses (item_type, league, analysis_timestamp)",
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
        
        logger.info("Database schema initialized successfully")
    
    def store_market_snapshot(self, snapshot: MarketSnapshot) -> int:
        """Store a market snapshot and return its ID."""
        with self.get_cursor() as cursor:
            cursor.execute("""
                INSERT INTO market_snapshots (
                    timestamp, item_type, league, base_price, currency,
                    median_price, min_price, max_price, total_listings,
                    active_listings, average_listing_age_hours, data_quality_score,
                    sample_size, confidence_score, price_volatility, market_activity_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                snapshot.timestamp,
                snapshot.item_type,
                snapshot.league,
                snapshot.base_price,
                snapshot.currency.value if snapshot.currency else None,
                snapshot.median_price,
                snapshot.min_price,
                snapshot.max_price,
                snapshot.total_listings,
                snapshot.active_listings,
                snapshot.average_listing_age_hours,
                snapshot.data_quality_score,
                snapshot.sample_size,
                snapshot.confidence_score,
                snapshot.price_volatility,
                snapshot.market_activity_score
            ))
            
            snapshot_id = cursor.lastrowid
            logger.debug(f"Stored market snapshot {snapshot_id} for {snapshot.item_type}")
            return snapshot_id
    
    def store_modifier_trend(self, trend: ModifierTrend) -> int:
        """Store a modifier trend and return its ID."""
        with self.get_cursor() as cursor:
            cursor.execute("""
                INSERT INTO modifier_trends (
                    modifier_id, modifier_text, item_type, league, timestamp,
                    base_value, currency, value_per_tier, value_per_point,
                    min_value, max_value, median_value, total_listings,
                    sample_size, confidence_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                trend.modifier_id,
                trend.modifier_text,
                trend.item_type,
                trend.league,
                trend.timestamp,
                trend.base_value,
                trend.currency.value if trend.currency else None,
                trend.value_per_tier,
                trend.value_per_point,
                trend.min_value,
                trend.max_value,
                trend.median_value,
                trend.total_listings,
                trend.sample_size,
                trend.confidence_score
            ))
            
            trend_id = cursor.lastrowid
            logger.debug(f"Stored modifier trend {trend_id} for {trend.modifier_id}")
            return trend_id
    
    def store_price_trend(self, trend: PriceTrend) -> int:
        """Store a price trend and return its ID."""
        with self.get_cursor() as cursor:
            cursor.execute("""
                INSERT INTO price_trends (
                    item_type, league, modifier_id, start_time, end_time,
                    time_frame, direction, price_change_percent, price_change_absolute,
                    currency, start_price, end_price, min_price, max_price,
                    average_price, median_price, price_volatility, max_drawdown,
                    max_gain, data_points, confidence_score, average_listings,
                    listing_velocity
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                trend.item_type,
                trend.league,
                trend.modifier_id,
                trend.start_time,
                trend.end_time,
                trend.time_frame.value,
                trend.direction.value,
                trend.price_change_percent,
                trend.price_change_absolute,
                trend.currency.value,
                trend.start_price,
                trend.end_price,
                trend.min_price,
                trend.max_price,
                trend.average_price,
                trend.median_price,
                trend.price_volatility,
                trend.max_drawdown,
                trend.max_gain,
                trend.data_points,
                trend.confidence_score,
                trend.average_listings,
                trend.listing_velocity
            ))
            
            trend_id = cursor.lastrowid
            logger.debug(f"Stored price trend {trend_id} for {trend.item_type}")
            return trend_id

    def get_market_snapshots(
        self,
        item_type: str,
        league: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[MarketSnapshot]:
        """Retrieve market snapshots for an item type."""
        with self.get_cursor() as cursor:
            query = """
                SELECT * FROM market_snapshots
                WHERE item_type = ? AND league = ?
            """
            params = [item_type, league]

            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time)

            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time)

            query += " ORDER BY timestamp DESC"

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            snapshots = []
            for row in rows:
                snapshot = MarketSnapshot(
                    id=row['id'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    item_type=row['item_type'],
                    league=row['league'],
                    base_price=row['base_price'],
                    currency=Currency(row['currency']) if row['currency'] else None,
                    median_price=row['median_price'],
                    min_price=row['min_price'],
                    max_price=row['max_price'],
                    total_listings=row['total_listings'],
                    active_listings=row['active_listings'],
                    average_listing_age_hours=row['average_listing_age_hours'],
                    data_quality_score=row['data_quality_score'],
                    sample_size=row['sample_size'],
                    confidence_score=row['confidence_score'],
                    price_volatility=row['price_volatility'],
                    market_activity_score=row['market_activity_score']
                )
                snapshots.append(snapshot)

            return snapshots

    def get_modifier_trends(
        self,
        modifier_id: str,
        item_type: str,
        league: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: Optional[int] = None
    ) -> List[ModifierTrend]:
        """Retrieve modifier trends."""
        with self.get_cursor() as cursor:
            query = """
                SELECT * FROM modifier_trends
                WHERE modifier_id = ? AND item_type = ? AND league = ?
            """
            params = [modifier_id, item_type, league]

            if start_time:
                query += " AND timestamp >= ?"
                params.append(start_time)

            if end_time:
                query += " AND timestamp <= ?"
                params.append(end_time)

            query += " ORDER BY timestamp DESC"

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            trends = []
            for row in rows:
                trend = ModifierTrend(
                    id=row['id'],
                    modifier_id=row['modifier_id'],
                    modifier_text=row['modifier_text'],
                    item_type=row['item_type'],
                    league=row['league'],
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    base_value=row['base_value'],
                    currency=Currency(row['currency']) if row['currency'] else None,
                    value_per_tier=row['value_per_tier'],
                    value_per_point=row['value_per_point'],
                    min_value=row['min_value'],
                    max_value=row['max_value'],
                    median_value=row['median_value'],
                    total_listings=row['total_listings'],
                    sample_size=row['sample_size'],
                    confidence_score=row['confidence_score']
                )
                trends.append(trend)

            return trends

    def get_price_trends(
        self,
        item_type: str,
        league: str,
        time_frame: Optional[TimeFrame] = None,
        modifier_id: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[PriceTrend]:
        """Retrieve price trends."""
        with self.get_cursor() as cursor:
            query = """
                SELECT * FROM price_trends
                WHERE item_type = ? AND league = ?
            """
            params = [item_type, league]

            if time_frame:
                query += " AND time_frame = ?"
                params.append(time_frame.value)

            if modifier_id is not None:
                query += " AND modifier_id = ?"
                params.append(modifier_id)

            query += " ORDER BY start_time DESC"

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            trends = []
            for row in rows:
                trend = PriceTrend(
                    id=row['id'],
                    item_type=row['item_type'],
                    league=row['league'],
                    modifier_id=row['modifier_id'],
                    start_time=datetime.fromisoformat(row['start_time']),
                    end_time=datetime.fromisoformat(row['end_time']),
                    time_frame=TimeFrame(row['time_frame']),
                    direction=TrendDirection(row['direction']),
                    price_change_percent=row['price_change_percent'],
                    price_change_absolute=row['price_change_absolute'],
                    currency=Currency(row['currency']),
                    start_price=row['start_price'],
                    end_price=row['end_price'],
                    min_price=row['min_price'],
                    max_price=row['max_price'],
                    average_price=row['average_price'],
                    median_price=row['median_price'],
                    price_volatility=row['price_volatility'],
                    max_drawdown=row['max_drawdown'],
                    max_gain=row['max_gain'],
                    data_points=row['data_points'],
                    confidence_score=row['confidence_score'],
                    average_listings=row['average_listings'],
                    listing_velocity=row['listing_velocity']
                )
                trends.append(trend)

            return trends

    def get_historical_market_data(
        self,
        item_type: str,
        league: str,
        days_back: int = 30
    ) -> HistoricalMarketData:
        """Get comprehensive historical market data for an item."""
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days_back)

        # Get market snapshots
        snapshots = self.get_market_snapshots(
            item_type=item_type,
            league=league,
            start_time=start_time,
            end_time=end_time
        )

        # Get all modifier trends for this item
        modifier_trends = {}
        with self.get_cursor() as cursor:
            cursor.execute("""
                SELECT DISTINCT modifier_id FROM modifier_trends
                WHERE item_type = ? AND league = ? AND timestamp >= ?
            """, (item_type, league, start_time))

            modifier_ids = [row[0] for row in cursor.fetchall()]

            for modifier_id in modifier_ids:
                trends = self.get_modifier_trends(
                    modifier_id=modifier_id,
                    item_type=item_type,
                    league=league,
                    start_time=start_time,
                    end_time=end_time
                )
                if trends:
                    modifier_trends[modifier_id] = trends

        return HistoricalMarketData(
            item_type=item_type,
            league=league,
            snapshots=snapshots,
            modifier_trends=modifier_trends
        )

    def cleanup_old_data(self, days_to_keep: int = 90):
        """Remove old data to keep database size manageable."""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)

        with self.get_cursor() as cursor:
            # Clean up old snapshots
            cursor.execute(
                "DELETE FROM market_snapshots WHERE timestamp < ?",
                (cutoff_date,)
            )
            snapshots_deleted = cursor.rowcount

            # Clean up old modifier trends
            cursor.execute(
                "DELETE FROM modifier_trends WHERE timestamp < ?",
                (cutoff_date,)
            )
            trends_deleted = cursor.rowcount

            # Clean up old price trends
            cursor.execute(
                "DELETE FROM price_trends WHERE start_time < ?",
                (cutoff_date,)
            )
            price_trends_deleted = cursor.rowcount

            # Clean up old trend analyses
            cursor.execute(
                "DELETE FROM trend_analyses WHERE analysis_timestamp < ?",
                (cutoff_date,)
            )
            analyses_deleted = cursor.rowcount

            # Vacuum database to reclaim space
            cursor.execute("VACUUM")

            logger.info(
                f"Cleaned up old data: {snapshots_deleted} snapshots, "
                f"{trends_deleted} modifier trends, {price_trends_deleted} price trends, "
                f"{analyses_deleted} analyses"
            )

    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        with self.get_cursor() as cursor:
            stats = {}

            # Count records in each table
            tables = ['market_snapshots', 'modifier_trends', 'price_trends', 'trend_analyses']
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                stats[f"{table}_count"] = cursor.fetchone()[0]

            # Get date ranges
            cursor.execute("""
                SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest
                FROM market_snapshots
            """)
            row = cursor.fetchone()
            if row['earliest']:
                stats['data_range'] = {
                    'earliest': row['earliest'],
                    'latest': row['latest']
                }

            # Get database file size
            stats['database_size_mb'] = self.db_path.stat().st_size / (1024 * 1024)

            # Get unique items and leagues
            cursor.execute("""
                SELECT COUNT(DISTINCT item_type) as items,
                       COUNT(DISTINCT league) as leagues
                FROM market_snapshots
            """)
            row = cursor.fetchone()
            stats['unique_items'] = row['items']
            stats['unique_leagues'] = row['leagues']

            return stats

    def get_trending_items(
        self,
        league: str,
        time_frame: TimeFrame = TimeFrame.DAY,
        min_change_percent: float = 10.0,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get items with significant price trends."""
        with self.get_cursor() as cursor:
            cursor.execute("""
                SELECT
                    item_type,
                    price_change_percent,
                    direction,
                    confidence_score,
                    average_listings,
                    start_price,
                    end_price,
                    currency
                FROM price_trends
                WHERE league = ?
                    AND time_frame = ?
                    AND ABS(price_change_percent) >= ?
                    AND confidence_score >= 0.5
                ORDER BY ABS(price_change_percent) DESC
                LIMIT ?
            """, (league, time_frame.value, min_change_percent, limit))

            trending = []
            for row in cursor.fetchall():
                trending.append({
                    'item_type': row['item_type'],
                    'price_change_percent': row['price_change_percent'],
                    'direction': row['direction'],
                    'confidence_score': row['confidence_score'],
                    'average_listings': row['average_listings'],
                    'start_price': row['start_price'],
                    'end_price': row['end_price'],
                    'currency': row['currency']
                })

            return trending

    def close(self):
        """Close database connections."""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')

        logger.info("Database connections closed")
