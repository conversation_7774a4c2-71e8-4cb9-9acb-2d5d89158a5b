#!/usr/bin/env python3
"""
Launch script for the PoE 2 Trade Valuation web interface.
"""

import sys
import webbrowser
import time
from pathlib import Path
from threading import Timer

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def open_browser():
    """Open the web browser after a short delay."""
    webbrowser.open('http://127.0.0.1:5000')

def main():
    """Launch the web application."""
    print("🎮 PoE 2 Trade Valuation Tool - Web Interface")
    print("=" * 50)
    
    try:
        from poe_trade.web import create_app
        
        # Create the Flask app
        app = create_app()
        
        # Check if configuration is loaded
        if 'CONFIG_ERROR' in app.config:
            print(f"⚠️  Configuration Error: {app.config['CONFIG_ERROR']}")
            print("\n📝 To fix this:")
            print("1. Run: python -m poe_trade.cli.main init-config")
            print("2. Edit config/config.yaml and set your POESESSID")
            print("3. Restart this application")
            print("\n🌐 Starting web interface anyway (you can fix config through the UI)...")
        else:
            print("✅ Configuration loaded successfully!")
            print("🔗 API client initialized")
        
        print(f"\n🌐 Starting web server at http://127.0.0.1:5000")
        print("📱 The web interface will open automatically in your browser")
        print("🛑 Press Ctrl+C to stop the server")
        
        # Open browser after 2 seconds
        Timer(2.0, open_browser).start()
        
        # Start the Flask development server
        app.run(
            debug=False,  # Set to False for cleaner output
            host='127.0.0.1',
            port=5000,
            use_reloader=False  # Disable reloader to prevent double startup
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n📦 Missing dependencies. Please install them:")
        print("pip install flask")
        return 1
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
