#!/usr/bin/env python3
"""
Test script to verify modifier matching functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poe_trade.data.precursor_modifiers import PRECURSOR_TABLET_DATA
from poe_trade.web.app import _modifier_text_matches

def test_modifier_matching():
    """Test the modifier matching function with sample data."""
    
    print("🧪 Testing Modifier Matching Function")
    print("=" * 50)
    
    # Test cases: (item_text, expected_text, should_match)
    test_cases = [
        # Quantity modifiers
        ("5% increased Quantity of Items found in your Maps", "(3–7)% increased Quantity of Items found in your Maps", True),
        ("6% increased Quantity of Items found in your Maps", "Plundering", False),  # Name vs text
        
        # Rarity modifiers  
        ("9% increased Rarity of Items found in your Maps", "(7–10)% increased Rarity of Items found in your Maps", True),
        ("8% increased Rarity of Items found in your Maps", "Collector's", False),  # Name vs text
        
        # Pack size
        ("5% increased Pack Size in your Maps", "(3–7)% increased Pack Size in your Maps", True),
        
        # Breach specific
        ("Breaches in your Maps spawn 20% increased Magic Monsters", "Breaches in your Maps spawn (15–25)% increased Magic Monsters", True),
        ("Breaches in your Maps contain 1 additional Clasped Hand", "Breaches in your Maps contain 1 additional Clasped Hand", True),
        
        # Should not match
        ("Some random modifier text", "(3–7)% increased Quantity of Items found in your Maps", False),
        ("Completely different text", "Breaches in your Maps spawn (15–25)% increased Magic Monsters", False),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for i, (item_text, expected_text, should_match) in enumerate(test_cases, 1):
        result = _modifier_text_matches(item_text, expected_text)
        status = "✅ PASS" if result == should_match else "❌ FAIL"
        
        print(f"\nTest {i}: {status}")
        print(f"  Item: '{item_text}'")
        print(f"  Expected: '{expected_text}'")
        print(f"  Should match: {should_match}, Got: {result}")
        
        if result == should_match:
            passed += 1
    
    print(f"\n" + "=" * 50)
    print(f"Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed - modifier matching may need improvement")

def show_sample_modifiers():
    """Show sample modifiers from each tablet type."""
    
    print("\n\n📋 Sample Modifiers by Tablet Type")
    print("=" * 50)
    
    for tablet_type, data in PRECURSOR_TABLET_DATA.items():
        print(f"\n🔸 {tablet_type}")
        print(f"   Base Effect: {data['base_effect']}")
        
        print("   Prefixes:")
        for mod in data['prefixes'][:3]:  # Show first 3
            print(f"     • {mod.name}: {mod.text}")
        
        print("   Suffixes:")
        for mod in data['suffixes'][:3]:  # Show first 3
            print(f"     • {mod.name}: {mod.text}")
        
        if len(data['prefixes']) > 3 or len(data['suffixes']) > 3:
            print("     ... (and more)")

if __name__ == "__main__":
    test_modifier_matching()
    show_sample_modifiers()
