"""
Market data analysis functionality.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from statistics import median, mean, stdev
from collections import defaultdict, Counter
from datetime import datetime

from ..models import (
    ItemListing, MarketData, PriceData, ModifierValue, 
    Currency, Modifier
)


logger = logging.getLogger(__name__)


class MarketAnalyzer:
    """Analyzes market data to determine item and modifier values."""
    
    def __init__(self, min_listings: int = 5, confidence_threshold: float = 0.6):
        """
        Initialize the market analyzer.
        
        Args:
            min_listings: Minimum number of listings required for analysis
            confidence_threshold: Minimum confidence score for valuations
        """
        self.min_listings = min_listings
        self.confidence_threshold = confidence_threshold
    
    def analyze_market_data(
        self, 
        item_type: str, 
        league: str, 
        listings: List[ItemListing]
    ) -> MarketData:
        """
        Analyze market data for a specific item type.
        
        Args:
            item_type: Type of item being analyzed
            league: League the data is from
            listings: List of item listings to analyze
            
        Returns:
            MarketData object with analysis results
        """
        logger.info(f"Analyzing {len(listings)} listings for {item_type}")
        
        if len(listings) < self.min_listings:
            logger.warning(f"Insufficient listings ({len(listings)}) for reliable analysis")
        
        # Create market data object
        market_data = MarketData(
            item_type=item_type,
            league=league,
            total_listings=len(listings)
        )
        
        # Analyze base prices
        base_price = self._analyze_base_prices(listings)
        market_data.base_price = base_price
        
        # Analyze modifier values
        modifier_values = self._analyze_modifier_values(listings)
        for mod_value in modifier_values:
            market_data.add_modifier_value(mod_value)
        
        # Calculate data quality score
        market_data.data_quality = self._calculate_data_quality(listings)
        
        logger.info(f"Analysis complete. Base price: {base_price}, {len(modifier_values)} modifiers analyzed")
        
        return market_data
    
    def _analyze_base_prices(self, listings: List[ItemListing]) -> Optional[PriceData]:
        """Analyze base prices from listings."""
        prices = []
        
        for listing in listings:
            if listing.price_value is not None and listing.price_currency:
                try:
                    currency = Currency(listing.price_currency)
                    price = PriceData(
                        currency=currency,
                        amount=listing.price_value,
                        confidence=1.0,
                        sample_size=1
                    )
                    prices.append(price)
                except ValueError:
                    # Unknown currency, skip
                    continue
        
        if not prices:
            return None
        
        # Group by currency
        currency_groups = defaultdict(list)
        for price in prices:
            currency_groups[price.currency].append(price.amount)
        
        # Use the most common currency
        most_common_currency = max(currency_groups.keys(), key=lambda c: len(currency_groups[c]))
        amounts = currency_groups[most_common_currency]
        
        if len(amounts) < self.min_listings:
            return None
        
        # Calculate statistics
        median_price = median(amounts)
        confidence = min(1.0, len(amounts) / 50.0)  # Higher confidence with more data
        
        return PriceData(
            currency=most_common_currency,
            amount=median_price,
            confidence=confidence,
            sample_size=len(amounts)
        )
    
    def _analyze_modifier_values(self, listings: List[ItemListing]) -> List[ModifierValue]:
        """Analyze modifier values from listings."""
        # Group listings by modifiers
        modifier_groups = self._group_by_modifiers(listings)
        
        modifier_values = []
        
        for mod_text, mod_listings in modifier_groups.items():
            if len(mod_listings) < self.min_listings:
                continue
            
            mod_value = self._calculate_modifier_value(mod_text, mod_listings)
            if mod_value and mod_value.base_value.confidence >= self.confidence_threshold:
                modifier_values.append(mod_value)
        
        return modifier_values
    
    def _group_by_modifiers(self, listings: List[ItemListing]) -> Dict[str, List[ItemListing]]:
        """Group listings by their modifiers."""
        modifier_groups = defaultdict(list)
        
        for listing in listings:
            # Get all modifiers from the item
            all_mods = listing.item.all_modifiers
            
            for modifier in all_mods:
                # Normalize modifier text for grouping
                normalized_text = self._normalize_modifier_text(modifier.text)
                modifier_groups[normalized_text].append(listing)
        
        return modifier_groups
    
    def _normalize_modifier_text(self, mod_text: str) -> str:
        """Normalize modifier text for consistent grouping."""
        # Replace numbers with placeholders for grouping
        import re
        
        # Replace specific numbers with #
        normalized = re.sub(r'\d+(?:\.\d+)?', '#', mod_text)
        
        # Clean up extra spaces
        normalized = ' '.join(normalized.split())
        
        return normalized
    
    def _calculate_modifier_value(
        self, 
        mod_text: str, 
        listings: List[ItemListing]
    ) -> Optional[ModifierValue]:
        """Calculate the value of a specific modifier."""
        # Extract prices
        prices = []
        for listing in listings:
            if listing.price_value is not None and listing.price_currency:
                try:
                    currency = Currency(listing.price_currency)
                    prices.append((listing.price_value, currency))
                except ValueError:
                    continue
        
        if not prices:
            return None
        
        # Group by currency and use most common
        currency_groups = defaultdict(list)
        for price, currency in prices:
            currency_groups[currency].append(price)
        
        most_common_currency = max(currency_groups.keys(), key=lambda c: len(currency_groups[c]))
        amounts = currency_groups[most_common_currency]
        
        if len(amounts) < self.min_listings:
            return None
        
        # Calculate statistics
        median_price = median(amounts)
        min_price = min(amounts)
        max_price = max(amounts)
        
        # Calculate confidence based on data consistency
        if len(amounts) > 1:
            price_stdev = stdev(amounts)
            cv = price_stdev / median_price if median_price > 0 else 1.0
            confidence = max(0.1, 1.0 - cv)  # Lower confidence for high variance
        else:
            confidence = 0.5
        
        # Adjust confidence based on sample size
        confidence *= min(1.0, len(amounts) / 20.0)
        
        base_value = PriceData(
            currency=most_common_currency,
            amount=median_price,
            confidence=confidence,
            sample_size=len(amounts)
        )
        
        min_price_data = PriceData(
            currency=most_common_currency,
            amount=min_price,
            confidence=confidence,
            sample_size=len(amounts)
        )
        
        max_price_data = PriceData(
            currency=most_common_currency,
            amount=max_price,
            confidence=confidence,
            sample_size=len(amounts)
        )
        
        return ModifierValue(
            modifier_id=mod_text,  # Using text as ID for now
            modifier_text=mod_text,
            base_value=base_value,
            min_price=min_price_data,
            max_price=max_price_data,
            median_price=base_value,
            total_listings=len(listings)
        )
    
    def _calculate_data_quality(self, listings: List[ItemListing]) -> float:
        """Calculate a data quality score based on various factors."""
        if not listings:
            return 0.0
        
        # Factors that affect data quality
        sample_size_score = min(1.0, len(listings) / 100.0)
        
        # Check for price data availability
        priced_listings = sum(1 for l in listings if l.price_value is not None)
        price_coverage = priced_listings / len(listings) if listings else 0.0
        
        # Check for recent data (assuming all data is recent for now)
        recency_score = 1.0
        
        # Combine scores
        quality_score = (sample_size_score * 0.4 + price_coverage * 0.4 + recency_score * 0.2)
        
        return quality_score
    
    def generate_valuation_report(self, market_data: MarketData) -> Dict[str, Any]:
        """Generate a comprehensive valuation report."""
        report = {
            "item_type": market_data.item_type,
            "league": market_data.league,
            "analysis_date": datetime.now().isoformat(),
            "data_quality": market_data.data_quality,
            "total_listings": market_data.total_listings,
            "base_price": None,
            "valuable_modifiers": [],
            "summary": {}
        }
        
        # Base price info
        if market_data.base_price:
            report["base_price"] = {
                "amount": market_data.base_price.amount,
                "currency": market_data.base_price.currency.value,
                "confidence": market_data.base_price.confidence,
                "sample_size": market_data.base_price.sample_size
            }
        
        # Top valuable modifiers
        sorted_modifiers = sorted(
            market_data.modifier_values.values(),
            key=lambda m: m.base_value.amount,
            reverse=True
        )
        
        for mod_value in sorted_modifiers[:10]:  # Top 10
            if mod_value.base_value.confidence >= self.confidence_threshold:
                report["valuable_modifiers"].append({
                    "text": mod_value.modifier_text,
                    "value": mod_value.base_value.amount,
                    "currency": mod_value.base_value.currency.value,
                    "confidence": mod_value.base_value.confidence,
                    "listings": mod_value.total_listings
                })
        
        # Summary statistics
        report["summary"] = {
            "analyzed_modifiers": len(market_data.modifier_values),
            "high_confidence_modifiers": len([
                m for m in market_data.modifier_values.values()
                if m.base_value.confidence >= self.confidence_threshold
            ]),
            "data_quality_rating": "High" if market_data.data_quality > 0.8 else 
                                  "Medium" if market_data.data_quality > 0.5 else "Low"
        }
        
        return report
