"""
Data models for search queries and results.
"""

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator

from .items import Item


class SearchStatus(str, Enum):
    """Search status options."""
    ANY = "any"
    ONLINE = "online"
    OFFLINE = "offline"


class SortOrder(str, Enum):
    """Sort order options."""
    ASC = "asc"
    DESC = "desc"


class SortBy(str, Enum):
    """Sort by options."""
    PRICE = "price"
    TIME = "time"
    LEVEL = "level"


class StatFilter(BaseModel):
    """Filter for item statistics/modifiers."""
    
    id: str
    value: Optional[Dict[str, Union[int, float]]] = None
    disabled: bool = False
    
    @validator('value', pre=True)
    def parse_value(cls, v):
        """Parse value filters."""
        if v is None:
            return None
        if isinstance(v, dict):
            return v
        if isinstance(v, (int, float)):
            return {"min": v}
        return None


class StatGroup(BaseModel):
    """Group of stat filters with logical operator."""
    
    type: str = "and"  # "and", "or", "weight", "weight2"
    filters: List[StatFilter] = Field(default_factory=list)
    disabled: bool = False
    
    def add_filter(self, stat_id: str, min_value: Optional[Union[int, float]] = None, 
                   max_value: Optional[Union[int, float]] = None, weight: Optional[int] = None):
        """Add a stat filter to this group."""
        value = {}
        if min_value is not None:
            value["min"] = min_value
        if max_value is not None:
            value["max"] = max_value
        if weight is not None:
            value["weight"] = weight
        
        filter_obj = StatFilter(
            id=stat_id,
            value=value if value else None
        )
        self.filters.append(filter_obj)


class TypeFilter(BaseModel):
    """Filter for item types."""
    
    category: Optional[str] = None
    rarity: Optional[str] = None
    
    def set_category(self, category: str):
        """Set the item category filter."""
        self.category = category


class TradeFilter(BaseModel):
    """Filter for trade-specific options."""
    
    price: Optional[Dict[str, str]] = None
    account: Optional[Dict[str, str]] = None
    
    def set_price_currency(self, currency: str):
        """Set the price currency filter."""
        if self.price is None:
            self.price = {}
        self.price["option"] = currency


class SearchFilters(BaseModel):
    """Container for all search filters."""
    
    type_filters: Optional[Dict[str, TypeFilter]] = None
    trade_filters: Optional[Dict[str, TradeFilter]] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        if self.type_filters is None:
            self.type_filters = {"filters": TypeFilter()}
        if self.trade_filters is None:
            self.trade_filters = {"filters": TradeFilter()}


class SearchQuery(BaseModel):
    """Complete search query for the trade API."""
    
    query: Dict[str, Any] = Field(default_factory=dict)
    sort: Optional[Dict[str, str]] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        if not self.query:
            self.query = {
                "stats": [],
                "status": {"option": "online"},
                "filters": SearchFilters().dict()
            }
    
    def add_stat_group(self, stat_group: StatGroup):
        """Add a stat group to the query."""
        if "stats" not in self.query:
            self.query["stats"] = []
        self.query["stats"].append(stat_group.dict())
    
    def set_status(self, status: SearchStatus):
        """Set the search status filter."""
        if "status" not in self.query:
            self.query["status"] = {}
        self.query["status"]["option"] = status.value
    
    def set_item_category(self, category: str):
        """Set the item category filter."""
        if "filters" not in self.query:
            self.query["filters"] = SearchFilters().dict()
        
        if "type_filters" not in self.query["filters"]:
            self.query["filters"]["type_filters"] = {"filters": {}}
        
        self.query["filters"]["type_filters"]["filters"]["category"] = {"option": category}
    
    def set_price_currency(self, currency: str):
        """Set the price currency filter."""
        if "filters" not in self.query:
            self.query["filters"] = SearchFilters().dict()
        
        if "trade_filters" not in self.query["filters"]:
            self.query["filters"]["trade_filters"] = {"filters": {}}
        
        self.query["filters"]["trade_filters"]["filters"]["price"] = {"option": currency}
    
    def set_sort(self, sort_by: SortBy, order: SortOrder = SortOrder.ASC):
        """Set the sort options."""
        self.sort = {sort_by.value: order.value}


class SearchResult(BaseModel):
    """Result from a search query."""
    
    id: str
    result: List[str] = Field(default_factory=list)
    total: Optional[int] = None
    inexact: bool = False
    
    @property
    def item_count(self) -> int:
        """Get the number of items found."""
        return len(self.result)
    
    def get_batch(self, start: int = 0, size: int = 10) -> List[str]:
        """Get a batch of item IDs."""
        return self.result[start:start + size]


class ItemListing(BaseModel):
    """Represents a single item listing from the market."""
    
    item: Item
    listing: Dict[str, Any]
    price: Optional[Dict[str, Any]] = None
    account: Optional[Dict[str, Any]] = None
    
    @property
    def price_value(self) -> Optional[float]:
        """Get the numeric price value."""
        if self.price and "amount" in self.price:
            return float(self.price["amount"])
        return None
    
    @property
    def price_currency(self) -> Optional[str]:
        """Get the price currency."""
        if self.price and "currency" in self.price:
            return self.price["currency"]
        return None
    
    @property
    def is_online(self) -> bool:
        """Check if the seller is online."""
        if self.account and "online" in self.account:
            return self.account["online"].get("status") == "online"
        return False
