"""
Market data collection functionality.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

from ..api import TradeAPIClient, APIError
from ..data.precursor_modifiers import PRECURSOR_TABLET_DATA, TABLET_TYPES
from ..models import (
    SearchQuery, SearchResult, ItemListing, Item, Modifier,
    MarketData, PriceData, Currency, ModifierType, SearchStatus
)


logger = logging.getLogger(__name__)


class MarketDataCollector:
    """Collects market data from the PoE 2 trade API."""
    
    def __init__(self, api_client: TradeAPIClient):
        """
        Initialize the market data collector.
        
        Args:
            api_client: Configured trade API client
        """
        self.api_client = api_client
    
    def collect_item_listings(
        self, 
        item_type: str, 
        league: str,
        max_results: int = 100,
        online_only: bool = True,
        currency_filter: Optional[str] = None
    ) -> List[ItemListing]:
        """
        Collect item listings for a specific item type.
        
        Args:
            item_type: Type of item to search for
            league: League to search in
            max_results: Maximum number of results to collect
            online_only: Only include online sellers
            currency_filter: Filter by specific currency
            
        Returns:
            List of item listings
        """
        logger.info(f"Collecting listings for {item_type} in {league}")
        
        try:
            # Create search query
            query = self._create_search_query(
                item_type=item_type,
                online_only=online_only,
                currency_filter=currency_filter
            )
            
            # Perform search
            logger.info(f"Search query: {query.query}")
            search_result = self.api_client.search(league, query.query)
            item_ids = search_result.get('result', [])
            
            if not item_ids:
                logger.warning(f"No items found for {item_type}")
                return []
            
            # Limit results
            item_ids = item_ids[:max_results]
            logger.info(f"Found {len(item_ids)} items, fetching details...")
            
            # Fetch item details in batches
            listings = []
            query_id = search_result.get('id', '')
            
            # API allows max 10 items per request
            batch_size = 10
            for i in range(0, len(item_ids), batch_size):
                batch_ids = item_ids[i:i + batch_size]
                
                try:
                    items_data = self.api_client.fetch_items(batch_ids, query_id)
                    batch_listings = self._parse_items_data(items_data)
                    listings.extend(batch_listings)
                    
                except APIError as e:
                    logger.error(f"Error fetching batch {i//batch_size + 1}: {e}")
                    continue
            
            logger.info(f"Successfully collected {len(listings)} listings")
            return listings
            
        except Exception as e:
            logger.error(f"Error collecting listings: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return []
    
    def collect_precursor_tablet_data(
        self, 
        league: str,
        tablet_types: Optional[List[str]] = None,
        max_results: int = 200
    ) -> Dict[str, List[ItemListing]]:
        """
        Collect market data specifically for precursor tablets.
        
        Args:
            league: League to search in
            tablet_types: Specific tablet types to search for
            max_results: Maximum results per tablet type
            
        Returns:
            Dictionary mapping tablet types to their listings
        """
        if tablet_types is None:
            tablet_types = TABLET_TYPES
        
        results = {}
        
        for tablet_type in tablet_types:
            logger.info(f"Collecting data for {tablet_type}")
            listings = self.collect_item_listings(
                item_type=tablet_type,
                league=league,
                max_results=max_results
            )
            results[tablet_type] = listings
        
        return results

    def collect_modifier_specific_data(
        self,
        league: str,
        modifier_families: Optional[List[str]] = None,
        max_results: int = 100
    ) -> Dict[str, Dict[str, List[ItemListing]]]:
        """
        Collect market data for tablets with specific modifier families.

        Args:
            league: League to search in
            modifier_families: List of modifier families to focus on
            max_results: Maximum results per tablet type

        Returns:
            Dictionary mapping tablet types to modifier families to listings
        """
        results = {}

        for tablet_type in TABLET_TYPES:
            results[tablet_type] = {}
            tablet_data = PRECURSOR_TABLET_DATA[tablet_type]

            # Collect all modifiers for this tablet type
            all_modifiers = tablet_data["prefixes"] + tablet_data["suffixes"]

            # Filter by families if specified
            if modifier_families:
                all_modifiers = [mod for mod in all_modifiers if mod.family in modifier_families]

            logger.info(f"Collecting modifier data for {tablet_type}")
            listings = self.collect_item_listings(
                item_type=tablet_type,
                league=league,
                max_results=max_results
            )

            # Group listings by modifier families
            for modifier in all_modifiers:
                family_listings = []
                for listing in listings:
                    if listing.item and listing.item.all_modifiers:
                        for item_mod in listing.item.all_modifiers:
                            # Check if this listing has a modifier from this family
                            if self._modifier_matches_family(item_mod.text, modifier):
                                family_listings.append(listing)
                                break

                results[tablet_type][modifier.family] = family_listings

        return results

    def _modifier_matches_family(self, item_mod_text: str, expected_modifier) -> bool:
        """Check if an item modifier matches an expected modifier family."""
        # Simple text matching - could be improved with regex
        key_words = expected_modifier.text.lower().split()
        item_text = item_mod_text.lower()

        # Check if key words from expected modifier are in the item modifier
        for word in key_words:
            if word in ["increased", "reduced", "more", "less", "%", "additional"]:
                continue  # Skip common words
            if word in item_text:
                return True

        return False

    def _create_search_query(
        self,
        item_type: str,
        online_only: bool = True,
        currency_filter: Optional[str] = None
    ) -> SearchQuery:
        """Create a search query for the given parameters."""
        query = SearchQuery()

        # Set the basic query structure
        # For precursor tablets, try different search approaches
        if "precursor" in item_type.lower() and "tablet" in item_type.lower():
            # Try searching by type only first
            query.query = {
                "query": {
                    "type": item_type
                },
                "sort": {
                    "price": "asc"
                }
            }
        else:
            query.query = {
                "query": {
                    "name": item_type,
                    "type": item_type
                },
                "sort": {
                    "price": "asc"
                }
            }

        # Set status filter
        if online_only:
            query.set_status(SearchStatus.ONLINE)

        # Set currency filter
        if currency_filter:
            query.set_price_currency(currency_filter)

        return query
    
    def _parse_items_data(self, items_data: Dict[str, Any]) -> List[ItemListing]:
        """Parse API response into ItemListing objects."""
        listings = []
        
        for item_data in items_data.get('result', []):
            try:
                # Parse item
                item_info = item_data.get('item', {})
                item = self._parse_item(item_info)
                
                # Parse listing info
                listing_info = item_data.get('listing', {})
                
                # Create listing
                listing = ItemListing(
                    item=item,
                    listing=listing_info,
                    price=listing_info.get('price'),
                    account=listing_info.get('account')
                )
                
                listings.append(listing)
                
            except Exception as e:
                logger.warning(f"Error parsing item data: {e}")
                import traceback
                logger.warning(f"Full traceback: {traceback.format_exc()}")
                continue
        
        return listings
    
    def _parse_item(self, item_data: Dict[str, Any]) -> Item:
        """Parse item data from API response."""
        # Parse modifiers
        explicit_mods = self._parse_modifiers(
            item_data.get('explicitMods', []), 
            ModifierType.EXPLICIT
        )
        implicit_mods = self._parse_modifiers(
            item_data.get('implicitMods', []), 
            ModifierType.IMPLICIT
        )
        crafted_mods = self._parse_modifiers(
            item_data.get('craftedMods', []), 
            ModifierType.CRAFTED
        )
        
        # Create item
        item = Item(
            id=item_data.get('id'),
            name=item_data.get('name', ''),
            type_line=item_data.get('typeLine', ''),
            base_type=item_data.get('baseType', item_data.get('typeLine', '')),
            rarity=item_data.get('rarity', 'Normal'),
            identified=item_data.get('identified', True),
            item_level=item_data.get('ilvl', 0),
            explicit_mods=explicit_mods,
            implicit_mods=implicit_mods,
            crafted_mods=crafted_mods,
            corrupted=item_data.get('corrupted', False),
            sanctified=item_data.get('sanctified', False),
            note=item_data.get('note'),
            league=item_data.get('league')
        )
        
        return item
    
    def _parse_modifiers(self, mod_texts: List[str], mod_type: ModifierType) -> List[Modifier]:
        """Parse modifier texts into Modifier objects."""
        modifiers = []
        
        for mod_text in mod_texts:
            try:
                modifier = Modifier(
                    text=mod_text,
                    type=mod_type
                )
                modifiers.append(modifier)
            except Exception as e:
                logger.warning(f"Error parsing modifier '{mod_text}': {e}")
                continue
        
        return modifiers
