{% extends "base.html" %}

{% block title %}Configuration - PoE 2 Trade Valuation{% endblock %}

{% block extra_css %}
<style>
    .config-section {
        border-left: 4px solid #d4af37;
        background: linear-gradient(90deg, rgba(212, 175, 55, 0.1) 0%, transparent 100%);
    }
    
    .config-field {
        transition: all 0.3s ease;
    }
    
    .config-field:hover {
        background-color: rgba(212, 175, 55, 0.05);
    }
    
    .config-help {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .test-result {
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .test-success {
        border-left: 4px solid #198754;
        background-color: rgba(25, 135, 84, 0.1);
    }
    
    .test-error {
        border-left: 4px solid #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }
    
    .test-warning {
        border-left: 4px solid #ffc107;
        background-color: rgba(255, 193, 7, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Configuration Header -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4 class="mb-0">
                    <i class="bi bi-gear"></i> Configuration
                </h4>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-success btn-sm" id="saveConfigBtn">
                        <i class="bi bi-check-circle"></i> Save Changes
                    </button>
                    <button class="btn btn-outline-warning btn-sm" id="resetConfigBtn">
                        <i class="bi bi-arrow-clockwise"></i> Reset to Defaults
                    </button>
                    <button class="btn btn-outline-info btn-sm" id="testConfigBtn">
                        <i class="bi bi-play-circle"></i> Test Configuration
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Configuration Form -->
    <div class="col-md-8">
        <form id="configForm">
            <!-- API Configuration -->
            <div class="card config-section mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-cloud"></i> API Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label for="poesessid" class="form-label">POESESSID Cookie</label>
                            <input type="password" class="form-control" id="poesessid" name="api.poesessid" required>
                            <div class="config-help">
                                Your PoE session ID cookie. Get this from your browser's developer tools when logged into pathofexile.com.
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="baseUrl" class="form-label">Base URL</label>
                            <input type="url" class="form-control" id="baseUrl" name="api.base_url" 
                                   value="https://www.pathofexile.com/api/trade2">
                            <div class="config-help">PoE trade API base URL</div>
                        </div>
                        <div class="col-md-6">
                            <label for="userAgent" class="form-label">User Agent</label>
                            <input type="text" class="form-control" id="userAgent" name="api.user_agent" 
                                   value="PoE2-Trade-Tool/1.0">
                            <div class="config-help">User agent string for API requests</div>
                        </div>
                        <div class="col-md-6">
                            <label for="rateLimit" class="form-label">Rate Limit (requests/minute)</label>
                            <input type="number" class="form-control" id="rateLimit" name="api.rate_limit" 
                                   min="1" max="60" value="45">
                            <div class="config-help">Maximum API requests per minute</div>
                        </div>
                        <div class="col-md-6">
                            <label for="timeout" class="form-label">Timeout (seconds)</label>
                            <input type="number" class="form-control" id="timeout" name="api.timeout" 
                                   min="5" max="60" value="30">
                            <div class="config-help">Request timeout in seconds</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- League Configuration -->
            <div class="card config-section mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-globe"></i> League Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="defaultLeague" class="form-label">Default League</label>
                            <select class="form-select" id="defaultLeague" name="league.default">
                                <option value="Rise of the Abyssal">Rise of the Abyssal</option>
                                <option value="Standard">Standard</option>
                                <option value="Hardcore">Hardcore</option>
                            </select>
                            <div class="config-help">Default league for searches</div>
                        </div>
                        <div class="col-md-6">
                            <label for="availableLeagues" class="form-label">Available Leagues</label>
                            <input type="text" class="form-control" id="availableLeagues" name="league.available" 
                                   value="Rise of the Abyssal,Standard,Hardcore">
                            <div class="config-help">Comma-separated list of available leagues</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Valuation Configuration -->
            <div class="card config-section mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-calculator"></i> Valuation Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="minListings" class="form-label">Minimum Listings</label>
                            <input type="number" class="form-control" id="minListings" name="valuation.min_listings" 
                                   min="1" max="100" value="5">
                            <div class="config-help">Minimum listings required for analysis</div>
                        </div>
                        <div class="col-md-4">
                            <label for="minConfidence" class="form-label">Minimum Confidence</label>
                            <input type="number" class="form-control" id="minConfidence" name="valuation.min_confidence" 
                                   min="0.1" max="1.0" step="0.1" value="0.5">
                            <div class="config-help">Minimum confidence threshold (0.1-1.0)</div>
                        </div>
                        <div class="col-md-4">
                            <label for="maxResults" class="form-label">Max Results per Search</label>
                            <input type="number" class="form-control" id="maxResults" name="valuation.max_results" 
                                   min="10" max="1000" value="200">
                            <div class="config-help">Maximum results to fetch per search</div>
                        </div>
                        <div class="col-md-6">
                            <label for="chaosToExaltRate" class="form-label">Chaos to Exalt Rate</label>
                            <input type="number" class="form-control" id="chaosToExaltRate" name="valuation.chaos_to_exalt_rate" 
                                   min="1" max="1000" value="180">
                            <div class="config-help">Exchange rate for currency conversion</div>
                        </div>
                        <div class="col-md-6">
                            <label for="outlierThreshold" class="form-label">Outlier Threshold</label>
                            <input type="number" class="form-control" id="outlierThreshold" name="valuation.outlier_threshold" 
                                   min="1" max="10" step="0.5" value="3.0">
                            <div class="config-help">Standard deviations for outlier detection</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Task Configuration -->
            <div class="card config-section mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list-task"></i> Task Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="maxConcurrentTasks" class="form-label">Max Concurrent Tasks</label>
                            <input type="number" class="form-control" id="maxConcurrentTasks" name="tasks.max_concurrent" 
                                   min="1" max="10" value="2">
                            <div class="config-help">Maximum tasks running simultaneously</div>
                        </div>
                        <div class="col-md-4">
                            <label for="taskRetries" class="form-label">Max Retries</label>
                            <input type="number" class="form-control" id="taskRetries" name="tasks.max_retries" 
                                   min="0" max="10" value="3">
                            <div class="config-help">Maximum retry attempts for failed tasks</div>
                        </div>
                        <div class="col-md-4">
                            <label for="taskTimeout" class="form-label">Task Timeout (minutes)</label>
                            <input type="number" class="form-control" id="taskTimeout" name="tasks.timeout_minutes" 
                                   min="1" max="60" value="10">
                            <div class="config-help">Maximum time for task execution</div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Test Panel -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-check-circle"></i> Configuration Test</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2 mb-3">
                    <button class="btn btn-outline-primary" id="testApiBtn">
                        <i class="bi bi-cloud-check"></i> Test API Connection
                    </button>
                    <button class="btn btn-outline-success" id="testSearchBtn">
                        <i class="bi bi-search"></i> Test Search Functionality
                    </button>
                    <button class="btn btn-outline-warning" id="testTasksBtn">
                        <i class="bi bi-gear-wide-connected"></i> Test Task System
                    </button>
                    <button class="btn btn-outline-info" id="runFullTestBtn">
                        <i class="bi bi-play-circle"></i> Run Full Test Suite
                    </button>
                </div>
                
                <div id="testResults" class="mt-3">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        Click a test button to validate your configuration.
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Configuration Status -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle"></i> Configuration Status</h6>
            </div>
            <div class="card-body">
                <div id="configStatus">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>API Configuration:</span>
                        <span class="badge bg-secondary" id="apiStatus">Unknown</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>League Settings:</span>
                        <span class="badge bg-secondary" id="leagueStatus">Unknown</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Valuation Settings:</span>
                        <span class="badge bg-secondary" id="valuationStatus">Unknown</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Task System:</span>
                        <span class="badge bg-secondary" id="taskStatus">Unknown</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Save Confirmation Modal -->
<div class="modal fade" id="saveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark border-warning">
            <div class="modal-header border-warning">
                <h5 class="modal-title text-warning">Save Configuration</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to save these configuration changes?</p>
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    The application will restart to apply the new settings.
                </div>
            </div>
            <div class="modal-footer border-warning">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmSaveBtn">Save & Restart</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentConfig = {};
    
    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        loadConfiguration();
        setupEventListeners();
        checkConfigurationStatus();
    });
    
    function setupEventListeners() {
        // Save configuration
        document.getElementById('saveConfigBtn').addEventListener('click', showSaveModal);
        document.getElementById('confirmSaveBtn').addEventListener('click', saveConfiguration);
        
        // Reset configuration
        document.getElementById('resetConfigBtn').addEventListener('click', resetConfiguration);
        
        // Test buttons
        document.getElementById('testConfigBtn').addEventListener('click', testCurrentConfig);
        document.getElementById('testApiBtn').addEventListener('click', testApiConnection);
        document.getElementById('testSearchBtn').addEventListener('click', testSearchFunctionality);
        document.getElementById('testTasksBtn').addEventListener('click', testTaskSystem);
        document.getElementById('runFullTestBtn').addEventListener('click', runFullTestSuite);
        
        // Form validation
        document.getElementById('configForm').addEventListener('input', validateForm);
    }
    
    async function loadConfiguration() {
        try {
            const response = await fetch('/api/config');
            const data = await response.json();
            
            if (data.success) {
                currentConfig = data.config;
                populateForm(currentConfig);
            } else {
                showTestResult('Failed to load configuration: ' + data.error, 'error');
            }
        } catch (error) {
            showTestResult('Error loading configuration: ' + error.message, 'error');
        }
    }
    
    function populateForm(config) {
        // Populate form fields based on configuration
        const form = document.getElementById('configForm');
        const inputs = form.querySelectorAll('input, select');
        
        inputs.forEach(input => {
            const path = input.name.split('.');
            let value = config;
            
            for (const key of path) {
                if (value && typeof value === 'object' && key in value) {
                    value = value[key];
                } else {
                    value = null;
                    break;
                }
            }
            
            if (value !== null) {
                if (input.type === 'checkbox') {
                    input.checked = Boolean(value);
                } else {
                    input.value = value;
                }
            }
        });
    }
    
    function showTestResult(message, type = 'info', details = null) {
        const resultsDiv = document.getElementById('testResults');
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 
                          type === 'success' ? 'alert-success' : 'alert-info';
        
        let html = `
            <div class="alert ${alertClass} test-result">
                <div class="d-flex align-items-start">
                    <i class="bi bi-${type === 'error' ? 'x-circle' : 
                                     type === 'warning' ? 'exclamation-triangle' : 
                                     type === 'success' ? 'check-circle' : 'info-circle'} me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div>${message}</div>
                        ${details ? `<pre class="mt-2 mb-0"><code>${JSON.stringify(details, null, 2)}</code></pre>` : ''}
                    </div>
                </div>
            </div>
        `;
        
        resultsDiv.innerHTML = html;
    }

    // Test functions
    async function testApiConnection() {
        const btn = document.getElementById('testApiBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';
        btn.disabled = true;

        try {
            const response = await fetch('/api/config/test/api', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                showToast('API connection test passed!', 'success');
            } else {
                showToast(`API test failed: ${data.error}`, 'error');
            }
        } catch (error) {
            showToast(`API test error: ${error.message}`, 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    async function testSearchFunctionality() {
        const btn = document.getElementById('testSearchBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';
        btn.disabled = true;

        try {
            const response = await fetch('/api/config/test/search', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                showToast('Search functionality test passed!', 'success');
            } else {
                showToast(`Search test failed: ${data.error}`, 'error');
            }
        } catch (error) {
            showToast(`Search test error: ${error.message}`, 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    async function testTaskSystem() {
        const btn = document.getElementById('testTasksBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Testing...';
        btn.disabled = true;

        try {
            const response = await fetch('/api/config/test/tasks', { method: 'POST' });
            const data = await response.json();

            if (data.success) {
                showToast('Task system test passed!', 'success');
            } else {
                showToast(`Task system test failed: ${data.error}`, 'error');
            }
        } catch (error) {
            showToast(`Task system test error: ${error.message}`, 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }

    async function runFullTestSuite() {
        const btn = document.getElementById('runFullTestBtn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Running Tests...';
        btn.disabled = true;

        try {
            showToast('Starting full test suite...', 'info');

            // Run all tests sequentially
            await testApiConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testSearchFunctionality();
            await new Promise(resolve => setTimeout(resolve, 1000));

            await testTaskSystem();

            showToast('Full test suite completed!', 'success');
        } catch (error) {
            showToast(`Test suite error: ${error.message}`, 'error');
        } finally {
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    }
</script>
{% endblock %}
