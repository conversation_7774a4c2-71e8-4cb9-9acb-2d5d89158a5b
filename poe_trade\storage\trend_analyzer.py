"""
Advanced trend analysis for historical market data.
"""

import logging
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from statistics import mean, median, stdev
from collections import defaultdict

from .models import (
    MarketSnapshot, ModifierTrend, PriceTrend, TrendAnalysis,
    TrendDirection, TimeFrame, HistoricalMarketData
)
from ..models import Currency


logger = logging.getLogger(__name__)


class TrendAnalyzer:
    """Advanced trend analysis for historical market data."""
    
    def __init__(self):
        """Initialize trend analyzer."""
        self.volatility_threshold = 0.3  # 30% volatility threshold
        self.trend_strength_threshold = 0.1  # 10% change for trend
        self.min_data_points = 5  # Minimum data points for analysis
    
    def analyze_comprehensive_trends(
        self,
        historical_data: HistoricalMarketData
    ) -> TrendAnalysis:
        """Perform comprehensive trend analysis on historical data."""
        logger.info(f"Analyzing trends for {historical_data.item_type} in {historical_data.league}")
        
        if len(historical_data.snapshots) < self.min_data_points:
            logger.warning(f"Insufficient data for trend analysis: {len(historical_data.snapshots)} snapshots")
            return self._create_empty_analysis(historical_data)
        
        # Calculate trends for different timeframes
        now = datetime.now()
        
        hourly_trend = historical_data.calculate_price_trend(TimeFrame.HOUR, now)
        daily_trend = historical_data.calculate_price_trend(TimeFrame.DAY, now)
        weekly_trend = historical_data.calculate_price_trend(TimeFrame.WEEK, now)
        monthly_trend = historical_data.calculate_price_trend(TimeFrame.MONTH, now)
        
        # Determine overall direction and strength
        overall_direction, trend_strength, trend_consistency = self._analyze_multi_timeframe_trends([
            hourly_trend, daily_trend, weekly_trend, monthly_trend
        ])
        
        # Calculate momentum and signals
        momentum_score = self._calculate_momentum(historical_data.snapshots)
        support_level, resistance_level = self._calculate_support_resistance(historical_data.snapshots)
        
        # Generate trading signals
        buy_signal, sell_signal, hold_recommendation = self._generate_trading_signals(
            daily_trend, momentum_score, support_level, resistance_level
        )
        
        # Calculate analysis confidence
        analysis_confidence = self._calculate_analysis_confidence(
            historical_data.snapshots, [hourly_trend, daily_trend, weekly_trend, monthly_trend]
        )
        
        # Determine if item is trending
        is_trending = self._is_trending(trend_strength, trend_consistency, analysis_confidence)
        
        return TrendAnalysis(
            item_type=historical_data.item_type,
            league=historical_data.league,
            hourly_trend=hourly_trend,
            daily_trend=daily_trend,
            weekly_trend=weekly_trend,
            monthly_trend=monthly_trend,
            overall_direction=overall_direction,
            trend_strength=trend_strength,
            trend_consistency=trend_consistency,
            is_trending=is_trending,
            momentum_score=momentum_score,
            support_level=support_level,
            resistance_level=resistance_level,
            buy_signal_strength=buy_signal,
            sell_signal_strength=sell_signal,
            hold_recommendation=hold_recommendation,
            analysis_confidence=analysis_confidence,
            data_coverage_days=self._calculate_data_coverage_days(historical_data.snapshots)
        )
    
    def _create_empty_analysis(self, historical_data: HistoricalMarketData) -> TrendAnalysis:
        """Create an empty trend analysis for insufficient data."""
        return TrendAnalysis(
            item_type=historical_data.item_type,
            league=historical_data.league,
            overall_direction=TrendDirection.STABLE,
            trend_strength=0.0,
            trend_consistency=0.0,
            is_trending=False,
            momentum_score=0.0,
            buy_signal_strength=0.0,
            sell_signal_strength=0.0,
            hold_recommendation=True,
            analysis_confidence=0.0,
            data_coverage_days=0
        )
    
    def _analyze_multi_timeframe_trends(
        self,
        trends: List[Optional[PriceTrend]]
    ) -> Tuple[TrendDirection, float, float]:
        """Analyze trends across multiple timeframes."""
        valid_trends = [t for t in trends if t is not None]
        
        if not valid_trends:
            return TrendDirection.STABLE, 0.0, 0.0
        
        # Count direction votes
        direction_votes = defaultdict(int)
        strength_values = []
        
        for trend in valid_trends:
            direction_votes[trend.direction] += 1
            strength_values.append(abs(trend.price_change_percent))
        
        # Determine overall direction
        overall_direction = max(direction_votes.keys(), key=lambda d: direction_votes[d])
        
        # Calculate trend strength (average of absolute changes)
        trend_strength = min(1.0, mean(strength_values) / 100.0) if strength_values else 0.0
        
        # Calculate consistency (how many timeframes agree)
        consistency = direction_votes[overall_direction] / len(valid_trends)
        
        return overall_direction, trend_strength, consistency
    
    def _calculate_momentum(self, snapshots: List[MarketSnapshot]) -> float:
        """Calculate price momentum score (-1 to 1)."""
        if len(snapshots) < 3:
            return 0.0
        
        # Sort by timestamp
        sorted_snapshots = sorted(snapshots, key=lambda s: s.timestamp)
        
        # Get prices (prefer median, fallback to base)
        prices = []
        for snapshot in sorted_snapshots:
            price = snapshot.median_price or snapshot.base_price
            if price is not None:
                prices.append(price)
        
        if len(prices) < 3:
            return 0.0
        
        # Calculate rate of change acceleration
        recent_third = len(prices) // 3
        recent_prices = prices[-recent_third:] if recent_third > 0 else prices[-1:]
        older_prices = prices[:recent_third] if recent_third > 0 else prices[:1]
        
        recent_avg = mean(recent_prices)
        older_avg = mean(older_prices)
        
        if older_avg == 0:
            return 0.0
        
        momentum = (recent_avg - older_avg) / older_avg
        
        # Normalize to -1 to 1 range
        return max(-1.0, min(1.0, momentum * 2))
    
    def _calculate_support_resistance(
        self,
        snapshots: List[MarketSnapshot]
    ) -> Tuple[Optional[float], Optional[float]]:
        """Calculate support and resistance levels."""
        if len(snapshots) < 5:
            return None, None
        
        # Get all prices
        prices = []
        for snapshot in snapshots:
            price = snapshot.median_price or snapshot.base_price
            if price is not None:
                prices.append(price)
        
        if len(prices) < 5:
            return None, None
        
        # Simple support/resistance calculation
        # Support: price level that has been tested multiple times from above
        # Resistance: price level that has been tested multiple times from below
        
        sorted_prices = sorted(prices)
        
        # Support is around the 25th percentile
        support_idx = len(sorted_prices) // 4
        support_level = sorted_prices[support_idx]
        
        # Resistance is around the 75th percentile
        resistance_idx = (len(sorted_prices) * 3) // 4
        resistance_level = sorted_prices[resistance_idx]
        
        return support_level, resistance_level
    
    def _generate_trading_signals(
        self,
        daily_trend: Optional[PriceTrend],
        momentum_score: float,
        support_level: Optional[float],
        resistance_level: Optional[float]
    ) -> Tuple[float, float, bool]:
        """Generate buy/sell signals and hold recommendation."""
        buy_signal = 0.0
        sell_signal = 0.0
        hold_recommendation = True
        
        if daily_trend is None:
            return buy_signal, sell_signal, hold_recommendation
        
        # Base signals on trend direction and momentum
        if daily_trend.direction == TrendDirection.RISING:
            buy_signal += 0.3
            if momentum_score > 0.2:
                buy_signal += 0.2
        elif daily_trend.direction == TrendDirection.FALLING:
            sell_signal += 0.3
            if momentum_score < -0.2:
                sell_signal += 0.2
        
        # Factor in support/resistance levels
        current_price = daily_trend.end_price
        
        if support_level and resistance_level:
            price_range = resistance_level - support_level
            if price_range > 0:
                # Near support = buy signal
                if current_price <= support_level * 1.05:  # Within 5% of support
                    buy_signal += 0.3
                
                # Near resistance = sell signal
                if current_price >= resistance_level * 0.95:  # Within 5% of resistance
                    sell_signal += 0.3
        
        # Factor in confidence
        confidence_multiplier = daily_trend.confidence_score
        buy_signal *= confidence_multiplier
        sell_signal *= confidence_multiplier
        
        # Normalize signals
        buy_signal = min(1.0, buy_signal)
        sell_signal = min(1.0, sell_signal)
        
        # Hold recommendation if signals are weak
        hold_recommendation = max(buy_signal, sell_signal) < 0.4
        
        return buy_signal, sell_signal, hold_recommendation
    
    def _calculate_analysis_confidence(
        self,
        snapshots: List[MarketSnapshot],
        trends: List[Optional[PriceTrend]]
    ) -> float:
        """Calculate confidence in the analysis."""
        confidence_factors = []
        
        # Data quantity factor
        data_quantity_score = min(1.0, len(snapshots) / 50.0)
        confidence_factors.append(data_quantity_score)
        
        # Data quality factor
        quality_scores = [s.data_quality_score for s in snapshots if s.data_quality_score > 0]
        if quality_scores:
            avg_quality = mean(quality_scores)
            confidence_factors.append(avg_quality)
        
        # Trend consistency factor
        valid_trends = [t for t in trends if t is not None]
        if valid_trends:
            trend_confidences = [t.confidence_score for t in valid_trends]
            avg_trend_confidence = mean(trend_confidences)
            confidence_factors.append(avg_trend_confidence)
        
        # Data recency factor
        if snapshots:
            latest_snapshot = max(snapshots, key=lambda s: s.timestamp)
            hours_since_latest = (datetime.now() - latest_snapshot.timestamp).total_seconds() / 3600
            recency_score = max(0.0, 1.0 - (hours_since_latest / 24.0))  # Decay over 24 hours
            confidence_factors.append(recency_score)
        
        return mean(confidence_factors) if confidence_factors else 0.0
    
    def _is_trending(
        self,
        trend_strength: float,
        trend_consistency: float,
        analysis_confidence: float
    ) -> bool:
        """Determine if an item is currently trending."""
        return (
            trend_strength > self.trend_strength_threshold and
            trend_consistency > 0.6 and
            analysis_confidence > 0.5
        )
    
    def _calculate_data_coverage_days(self, snapshots: List[MarketSnapshot]) -> int:
        """Calculate how many days of data we have."""
        if not snapshots:
            return 0
        
        earliest = min(snapshots, key=lambda s: s.timestamp)
        latest = max(snapshots, key=lambda s: s.timestamp)
        
        return (latest.timestamp - earliest.timestamp).days
    
    def detect_anomalies(
        self,
        snapshots: List[MarketSnapshot],
        z_threshold: float = 3.0
    ) -> List[Dict[str, Any]]:
        """Detect price anomalies using statistical methods."""
        if len(snapshots) < 10:
            return []
        
        # Extract prices
        prices = []
        timestamps = []
        
        for snapshot in snapshots:
            price = snapshot.median_price or snapshot.base_price
            if price is not None:
                prices.append(price)
                timestamps.append(snapshot.timestamp)
        
        if len(prices) < 10:
            return []
        
        # Calculate z-scores
        price_mean = mean(prices)
        price_std = stdev(prices)
        
        if price_std == 0:
            return []
        
        anomalies = []
        for i, (price, timestamp) in enumerate(zip(prices, timestamps)):
            z_score = abs(price - price_mean) / price_std
            
            if z_score > z_threshold:
                anomalies.append({
                    'timestamp': timestamp,
                    'price': price,
                    'z_score': z_score,
                    'deviation_percent': ((price - price_mean) / price_mean) * 100,
                    'type': 'spike' if price > price_mean else 'dip'
                })
        
        return anomalies
