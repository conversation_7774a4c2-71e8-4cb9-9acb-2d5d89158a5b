"""Task manager with smart rate limiting for PoE API operations."""

import asyncio
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import deque
import logging

from ..models.tasks import Task, TaskStatus, TaskType, TaskQueue, TaskResult, TaskPriority
from ..api.client import TradeAPIClient
from ..api.exceptions import RateLimitError, APIError


logger = logging.getLogger(__name__)


class RateLimiter:
    """Smart rate limiter for PoE API calls."""
    
    def __init__(self, max_requests: int = 45, window_seconds: int = 60):
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = deque()
        self.lock = threading.Lock()
        
        # Adaptive rate limiting
        self.current_limit = max_requests
        self.consecutive_rate_limits = 0
        self.last_rate_limit = None
        
    def can_make_request(self) -> bool:
        """Check if we can make a request without hitting rate limit."""
        with self.lock:
            now = time.time()
            
            # Remove old requests outside the window
            while self.requests and self.requests[0] < now - self.window_seconds:
                self.requests.popleft()
            
            # Check if we're under the current limit
            return len(self.requests) < self.current_limit
    
    def record_request(self):
        """Record a successful API request."""
        with self.lock:
            self.requests.append(time.time())
            
            # Reset consecutive rate limits on successful request
            if self.consecutive_rate_limits > 0:
                self.consecutive_rate_limits = 0
                # Gradually increase limit back to normal
                self.current_limit = min(self.max_requests, self.current_limit + 1)
    
    def record_rate_limit(self):
        """Record a rate limit hit and adjust limits."""
        with self.lock:
            self.consecutive_rate_limits += 1
            self.last_rate_limit = time.time()
            
            # Reduce current limit more aggressively with consecutive hits
            reduction = min(10, self.consecutive_rate_limits * 2)
            self.current_limit = max(5, self.current_limit - reduction)
            
            logger.warning(f"Rate limit hit #{self.consecutive_rate_limits}. "
                         f"Reduced limit to {self.current_limit}")
    
    def get_wait_time(self) -> float:
        """Get recommended wait time before next request."""
        with self.lock:
            if not self.requests:
                return 0
            
            # Base wait time
            oldest_request = self.requests[0]
            time_until_window_reset = max(0, self.window_seconds - (time.time() - oldest_request))
            
            # If we're at the limit, wait for the oldest request to expire
            if len(self.requests) >= self.current_limit:
                return time_until_window_reset + 1
            
            # Adaptive wait based on recent rate limits
            if self.consecutive_rate_limits > 0:
                # Exponential backoff for consecutive rate limits
                backoff = min(60, 2 ** self.consecutive_rate_limits)
                return backoff
            
            # Normal spacing between requests
            return max(0, self.window_seconds / self.current_limit - 1)


class TaskManager:
    """Manages task execution with smart rate limiting and scheduling."""
    
    def __init__(self, api_client: TradeAPIClient):
        self.api_client = api_client
        self.task_queue = TaskQueue()
        self.rate_limiter = RateLimiter()
        
        # Task execution
        self.running = False
        self.worker_thread = None
        self.max_concurrent_tasks = 2  # Conservative for API safety
        
        # Task handlers
        self.task_handlers: Dict[TaskType, Callable] = {}
        self.register_default_handlers()
        
        # Statistics
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'api_calls_made': 0,
            'rate_limit_hits': 0,
            'average_execution_time': 0.0
        }
    
    def register_default_handlers(self):
        """Register default task handlers."""
        from .handlers import (
            handle_precursor_analysis,
            handle_item_search,
            handle_market_data_collection,
            handle_api_test
        )
        
        self.task_handlers[TaskType.PRECURSOR_ANALYSIS] = handle_precursor_analysis
        self.task_handlers[TaskType.ITEM_SEARCH] = handle_item_search
        self.task_handlers[TaskType.MARKET_DATA_COLLECTION] = handle_market_data_collection
        self.task_handlers[TaskType.API_TEST] = handle_api_test
    
    def start(self):
        """Start the task manager."""
        if self.running:
            return
        
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        logger.info("Task manager started")
    
    def stop(self):
        """Stop the task manager."""
        self.running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        logger.info("Task manager stopped")
    
    def submit_task(self, task: Task) -> str:
        """Submit a task for execution."""
        self.task_queue.add_task(task)
        self.stats['total_tasks'] += 1
        
        task.add_log("INFO", f"Task submitted to queue. Position: {len(self.task_queue.get_pending_tasks())}")
        logger.info(f"Task submitted: {task.name} ({task.id})")
        
        return task.id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """Get task by ID."""
        return self.task_queue.get_task(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """Get all tasks."""
        return self.task_queue.tasks.copy()
    
    def pause_task(self, task_id: str) -> bool:
        """Pause a running task."""
        task = self.get_task(task_id)
        if task and task.status == TaskStatus.RUNNING:
            task.update_status(TaskStatus.PAUSED, "Task paused by user")
            return True
        return False
    
    def resume_task(self, task_id: str) -> bool:
        """Resume a paused task."""
        task = self.get_task(task_id)
        if task and task.status == TaskStatus.PAUSED:
            task.update_status(TaskStatus.PENDING, "Task resumed by user")
            return True
        return False
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a task."""
        task = self.get_task(task_id)
        if task and not task.is_terminal:
            task.update_status(TaskStatus.CANCELLED, "Task cancelled by user")
            return True
        return False
    
    def restart_task(self, task_id: str) -> bool:
        """Restart a failed or cancelled task."""
        task = self.get_task(task_id)
        if task and task.status in [TaskStatus.FAILED, TaskStatus.CANCELLED]:
            # Reset task state
            task.status = TaskStatus.PENDING
            task.retry_count = 0
            task.next_retry_at = None
            task.started_at = None
            task.completed_at = None
            task.result = None
            task.progress.completed_steps = 0
            task.progress.percentage = 0.0
            task.add_log("INFO", "Task restarted by user")
            return True
        return False
    
    def _worker_loop(self):
        """Main worker loop for task execution."""
        logger.info("Task worker loop started")
        
        while self.running:
            try:
                # Check if we can run more tasks
                running_tasks = self.task_queue.get_running_tasks()
                if len(running_tasks) >= self.max_concurrent_tasks:
                    time.sleep(1)
                    continue
                
                # Get next task
                next_task = self.task_queue.get_next_task()
                if not next_task:
                    time.sleep(1)
                    continue
                
                # Check rate limiting
                if not self.rate_limiter.can_make_request():
                    wait_time = self.rate_limiter.get_wait_time()
                    if wait_time > 0:
                        logger.info(f"Rate limiting: waiting {wait_time:.1f}s before next task")
                        time.sleep(min(wait_time, 5))  # Cap wait time for responsiveness
                        continue
                
                # Execute task
                self._execute_task(next_task)
                
            except Exception as e:
                logger.error(f"Error in worker loop: {e}", exc_info=True)
                time.sleep(1)
        
        logger.info("Task worker loop stopped")
    
    def _execute_task(self, task: Task):
        """Execute a single task."""
        try:
            # Update task status
            task.update_status(TaskStatus.RUNNING, "Task execution started")
            
            # Get task handler
            handler = self.task_handlers.get(task.task_type)
            if not handler:
                raise ValueError(f"No handler registered for task type: {task.task_type}")
            
            # Execute task with rate limiting awareness
            start_time = time.time()
            result = handler(task, self.api_client, self.rate_limiter)
            execution_time = time.time() - start_time
            
            # Update statistics
            self.stats['completed_tasks'] += 1
            self.stats['api_calls_made'] += result.api_calls_made
            self.stats['rate_limit_hits'] += result.rate_limit_hits
            
            # Update average execution time
            total_completed = self.stats['completed_tasks']
            current_avg = self.stats['average_execution_time']
            self.stats['average_execution_time'] = (
                (current_avg * (total_completed - 1) + execution_time) / total_completed
            )
            
            # Set task result
            result.execution_time = execution_time
            task.set_result(result)
            
            logger.info(f"Task completed: {task.name} ({task.id}) in {execution_time:.2f}s")
            
        except Exception as e:
            # Handle task failure
            self.stats['failed_tasks'] += 1
            
            error_msg = str(e)
            task.add_log("ERROR", f"Task execution failed: {error_msg}")
            
            # Check if we should retry
            if task.retry_count < task.max_retries and isinstance(e, (RateLimitError, APIError)):
                delay = min(300, 60 * (2 ** task.retry_count))  # Exponential backoff, max 5 min
                task.schedule_retry(delay)
                logger.warning(f"Task failed, scheduling retry: {task.name} ({task.id})")
            else:
                # Final failure
                result = TaskResult(
                    success=False,
                    error=error_msg,
                    api_calls_made=0,
                    rate_limit_hits=1 if isinstance(e, RateLimitError) else 0
                )
                task.set_result(result)
                logger.error(f"Task failed permanently: {task.name} ({task.id})")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get task manager statistics."""
        running_count = len(self.task_queue.get_running_tasks())
        pending_count = len(self.task_queue.get_pending_tasks())
        
        return {
            **self.stats,
            'running_tasks': running_count,
            'pending_tasks': pending_count,
            'current_rate_limit': self.rate_limiter.current_limit,
            'consecutive_rate_limits': self.rate_limiter.consecutive_rate_limits,
            'queue_size': len(self.task_queue.tasks)
        }
