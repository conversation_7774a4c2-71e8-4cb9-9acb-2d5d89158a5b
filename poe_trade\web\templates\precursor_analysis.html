{% extends "base.html" %}

{% block title %}Precursor Tablet Analysis - PoE 2 Trade Valuation{% endblock %}

{% block extra_css %}
<style>
    .spin {
        animation: spin 2s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .progress {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .card.bg-secondary {
        background-color: rgba(108, 117, 125, 0.3) !important;
    }

    .font-monospace {
        font-family: 'Courier New', monospace;
        line-height: 1.4;
    }

    .modal-lg {
        max-width: 800px;
    }

    #logEntries {
        max-height: 120px;
        overflow-y: auto;
    }

    #logEntries::-webkit-scrollbar {
        width: 6px;
    }

    #logEntries::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    #logEntries::-webkit-scrollbar-thumb {
        background: #d4af37;
        border-radius: 3px;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Analysis Controls -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-gem"></i> Precursor Tablet Modifier Analysis
                </h4>
            </div>
            <div class="card-body">
                <div class="row align-items-end">
                    <div class="col-md-3">
                        <label for="analysisLeague" class="form-label">League</label>
                        <select class="form-select" id="analysisLeague">
                            {% for league in leagues %}
                            <option value="{{ league }}" {% if league == default_league %}selected{% endif %}>
                                {{ league }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="sampleSize" class="form-label">Sample Size</label>
                        <select class="form-select" id="sampleSize">
                            <option value="200" selected>200 items per type</option>
                            <option value="300">300 items per type</option>
                            <option value="500">500 items per type</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="minConfidence" class="form-label">Min Confidence</label>
                        <select class="form-select" id="minConfidence">
                            <option value="0.3">30%</option>
                            <option value="0.5" selected>50%</option>
                            <option value="0.7">70%</option>
                            <option value="0.8">80%</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <button type="button" class="btn btn-warning w-100" id="analyzeBtn">
                            <i class="bi bi-graph-up"></i> Analyze All Modifiers
                        </button>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>Analysis Overview:</strong> This will analyze all precursor tablet types and their modifiers to determine current market values. Higher sample sizes provide more accurate results but take longer to process.
                    </div>
                </div>
            </div>

            <!-- Regex Generator Section -->
            <div class="col-md-6">
                <div class="card glass-effect">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-search me-2"></i>Stash Search Regex Generator</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">Generate regex patterns to search for valuable modifiers in your stash (50 char limit).</p>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="minValue" class="form-label">Min Value (chaos)</label>
                                <input type="number" class="form-control" id="minValue" value="10" min="1" max="100">
                            </div>
                            <div class="col-md-6">
                                <label for="maxPatterns" class="form-label">Max Patterns</label>
                                <input type="number" class="form-control" id="maxPatterns" value="5" min="1" max="10">
                            </div>
                            <div class="col-12">
                                <button type="button" class="btn btn-info w-100" id="generateRegexBtn">
                                    <i class="bi bi-code-slash"></i> Generate Regex Patterns
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Regex Results Section -->
<div class="row mt-4" id="regexSection" style="display: none;">
    <div class="col-12">
        <div class="card glass-effect">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-regex me-2"></i>Generated Regex Patterns</h5>
            </div>
            <div class="card-body">
                <div id="regexResults">
                    <!-- Regex patterns will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="row" id="resultsSection" style="display: none;">
    <!-- Summary Cards -->
    <div class="col-12 mb-4">
        <div class="row" id="summaryCards">
            <!-- Summary cards will be populated here -->
        </div>
    </div>
    
    <!-- Tablet Analysis Section -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-collection"></i> Modifier Analysis by Tablet Type
                    <small class="text-muted ms-2">(Prices in Exalted Orbs)</small>
                </h5>
            </div>
            <div class="card-body">
                <div id="tabletAnalysisContainer">
                    <!-- Tablet analysis will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Synergy Analysis -->
    <div class="col-12 mb-4" id="synergySection" style="display: none;">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-diagram-3"></i> Modifier Synergies
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="bi bi-lightbulb"></i>
                    <strong>Synergy Detection:</strong> These modifier combinations are worth more together than separately.
                </div>
                <div id="synergyContent">
                    <!-- Synergy data will be populated here -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Detailed Breakdown by Tablet Type -->
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-collection"></i> Breakdown by Tablet Type
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="tabletAccordion">
                    <!-- Tablet type breakdowns will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-dark border-warning">
            <div class="modal-header border-warning">
                <h5 class="modal-title text-warning">
                    <i class="bi bi-gear-fill spin me-2"></i>
                    Analyzing Precursor Modifiers
                </h5>
            </div>
            <div class="modal-body">
                <!-- Progress Overview -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold text-light">Overall Progress</span>
                        <span id="overallProgress" class="text-warning">0%</span>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-warning" role="progressbar" id="overallProgressBar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Current Step -->
                <div class="mb-4">
                    <h6 class="text-warning mb-3">
                        <i class="bi bi-arrow-right-circle me-2"></i>
                        Current Step: <span id="currentStep">Initializing...</span>
                    </h6>

                    <!-- Step Details -->
                    <div class="card bg-secondary border-warning">
                        <div class="card-body p-3">
                            <div id="stepDetails" class="font-monospace small text-light">
                                Preparing analysis...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tablet Collection Progress -->
                <div class="mb-4" id="tabletProgress" style="display: none;">
                    <h6 class="text-info mb-3">
                        <i class="bi bi-collection me-2"></i>
                        Tablet Data Collection
                    </h6>
                    <div id="tabletList">
                        <!-- Tablet progress items will be added here -->
                    </div>
                </div>

                <!-- Analysis Statistics -->
                <div class="mb-3" id="analysisStats" style="display: none;">
                    <h6 class="text-success mb-3">
                        <i class="bi bi-graph-up me-2"></i>
                        Analysis Statistics
                    </h6>
                    <div class="row g-2">
                        <div class="col-md-3">
                            <div class="card bg-secondary border-warning">
                                <div class="card-body p-2 text-center">
                                    <div class="h5 mb-1 text-warning" id="totalListings">0</div>
                                    <small class="text-muted">Total Listings</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary border-warning">
                                <div class="card-body p-2 text-center">
                                    <div class="h5 mb-1 text-info" id="totalModifiers">0</div>
                                    <small class="text-muted">Raw Modifiers</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary border-warning">
                                <div class="card-body p-2 text-center">
                                    <div class="h5 mb-1 text-success" id="matchedModifiers">0</div>
                                    <small class="text-muted">Matched</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary border-warning">
                                <div class="card-body p-2 text-center">
                                    <div class="h5 mb-1 text-warning" id="uniqueModifiers">0</div>
                                    <small class="text-muted">Unique</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Log -->
                <div id="activityLog" style="display: none;">
                    <h6 class="text-secondary mb-2">
                        <i class="bi bi-list-ul me-2"></i>
                        Activity Log
                    </h6>
                    <div class="card bg-secondary border-warning" style="max-height: 150px; overflow-y: auto;">
                        <div class="card-body p-2">
                            <div id="logEntries" class="font-monospace small text-light">
                                <!-- Log entries will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentAnalysisData = null;
    
    // Analysis button handler
    document.getElementById('analyzeBtn').addEventListener('click', async function() {
        const league = document.getElementById('analysisLeague').value;
        const sampleSize = parseInt(document.getElementById('sampleSize').value);
        const minConfidence = parseFloat(document.getElementById('minConfidence').value);
        
        // Show loading modal and reset progress
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        resetProgressState();
        loadingModal.show();

        // Start detailed progress tracking
        updateProgress('Initializing Analysis', 5,
            `Starting analysis for league: ${league}<br>Sample size: ${sampleSize} per tablet<br>Min confidence: ${minConfidence}%`);

        try {
            // Simulate tablet collection progress
            const tabletTypes = ['Breach Precursor Tablet', 'Delirium Precursor Tablet', 'Precursor Tablet',
                               'Expedition Precursor Tablet', 'Ritual Precursor Tablet', 'Overseer Precursor Tablet'];

            updateProgress('Preparing API Request', 10,
                `Configuring search parameters<br>Target tablets: ${tabletTypes.length}<br>League: ${league}`);

            // Initialize tablet progress tracking
            tabletTypes.forEach(tablet => {
                updateTabletProgress(tablet, 'pending');
            });

            updateProgress('Sending API Request', 15,
                'Contacting PoE 2 Trade API...<br>This may take a moment due to rate limiting');

            // Create a task for the analysis
            const taskResponse = await fetch('/api/precursor-modifier-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    league: league,
                    max_results: sampleSize,
                    min_confidence: minConfidence
                })
            });

            updateProgress('Processing Task Creation', 25,
                'Task created successfully<br>Starting analysis...');

            const taskData = await taskResponse.json();

            if (taskData.success) {
                const taskId = taskData.task_id;

                updateProgress('Task Created', 30,
                    `Task ID: ${taskId}<br>Estimated time: ${taskData.estimated_time || '2-5 minutes'}`);

                // Poll for task completion
                await pollTaskProgress(taskId, loadingModal, tabletTypes);
            } else {
                updateProgress('Task Creation Failed', 0,
                    `❌ Error: ${taskData.error}<br>Please check your configuration and try again`);
                setTimeout(() => {
                    loadingModal.hide();
                    showToast(taskData.error, 'error');
                }, 2000);
            }
        } catch (error) {
            updateProgress('Connection Failed', 0,
                `❌ Network Error: ${error.message}<br>Please check your internet connection and try again`);
            setTimeout(() => {
                loadingModal.hide();
                showToast('Analysis failed: ' + error.message, 'error');
            }, 2000);
        }
    });

    // Task progress polling function
    async function pollTaskProgress(taskId, loadingModal, tabletTypes) {
        const pollInterval = 2000; // Poll every 2 seconds
        let lastProgress = 0;

        const poll = async () => {
            try {
                const response = await fetch(`/api/tasks/${taskId}`);
                const taskData = await response.json();

                if (taskData.success) {
                    const task = taskData.task;
                    const progress = task.progress || 0;
                    const status = task.status;

                    // Update progress display
                    if (task.current_step) {
                        updateProgress(task.current_step, progress, task.details || '');
                    }

                    // Update statistics if available
                    if (task.statistics) {
                        const stats = task.statistics;
                        if (stats.total_listings) {
                            updateProgress(task.current_step || 'Processing', progress,
                                `Found ${stats.total_listings} total listings<br>Processing modifier data...`,
                                { totalListings: stats.total_listings });
                        }
                        if (stats.raw_modifiers) {
                            updateProgress(task.current_step || 'Analyzing', progress,
                                `Matching ${stats.raw_modifiers} raw modifiers<br>Identifying unique patterns...`,
                                {
                                    totalModifiers: stats.raw_modifiers,
                                    matchedModifiers: stats.matched_modifiers || 0
                                });
                        }
                    }

                    // Update tablet progress if available
                    if (task.tablet_progress) {
                        for (const [tablet, tabletStatus] of Object.entries(task.tablet_progress)) {
                            updateTabletProgress(tablet, tabletStatus.status, tabletStatus.count);
                        }
                    }

                    if (status === 'COMPLETED') {
                        // Get the final result
                        const resultResponse = await fetch(`/api/tasks/${taskId}/result`);
                        const resultData = await resultResponse.json();

                        if (resultData.success) {
                            currentAnalysisData = resultData.result;
                            displayAnalysisResults(resultData.result);
                            updateProgress('Analysis Complete', 100,
                                `✅ Successfully analyzed ${resultData.result.total_modifiers || 0} unique modifiers<br>🔍 Found ${resultData.result.synergies?.length || 0} synergies<br>📊 Ready for review!`);

                            setTimeout(() => {
                                loadingModal.hide();
                                showToast(`Analysis complete! Found ${resultData.result.total_modifiers || 0} unique modifiers.`, 'success');
                            }, 2000);
                        } else {
                            throw new Error(resultData.error || 'Failed to get task result');
                        }
                    } else if (status === 'FAILED') {
                        throw new Error(task.error || 'Task failed');
                    } else if (status === 'CANCELLED') {
                        throw new Error('Task was cancelled');
                    } else {
                        // Continue polling
                        setTimeout(poll, pollInterval);
                    }
                } else {
                    throw new Error(taskData.error || 'Failed to get task status');
                }
            } catch (error) {
                updateProgress('Task Failed', 0,
                    `❌ Error: ${error.message}<br>Please check your configuration and try again`);
                setTimeout(() => {
                    loadingModal.hide();
                    showToast('Analysis failed: ' + error.message, 'error');
                }, 2000);
            }
        };

        // Start polling
        setTimeout(poll, pollInterval);
    }

    // Sort handlers
    document.querySelectorAll('input[name="sortBy"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (currentAnalysisData) {
                sortAndDisplayModifiers(this.value);
            }
        });
    });
    
    // Enhanced progress tracking
    let progressState = {
        currentStep: 'Initializing',
        overallProgress: 0,
        tabletProgress: {},
        stats: {
            totalListings: 0,
            totalModifiers: 0,
            matchedModifiers: 0,
            uniqueModifiers: 0
        },
        logs: []
    };

    function updateProgress(step, progress, details = null, stats = null) {
        // Update current step
        progressState.currentStep = step;
        progressState.overallProgress = progress;

        // Update UI elements
        document.getElementById('currentStep').textContent = step;
        document.getElementById('overallProgress').textContent = progress + '%';
        document.getElementById('overallProgressBar').style.width = progress + '%';

        // Update step details
        if (details) {
            document.getElementById('stepDetails').innerHTML = details;
        }

        // Update statistics
        if (stats) {
            Object.assign(progressState.stats, stats);
            updateStatsDisplay();
        }

        // Add log entry
        addLogEntry(`[${new Date().toLocaleTimeString()}] ${step}: ${details || 'In progress...'}`);
    }

    function updateStatsDisplay() {
        document.getElementById('totalListings').textContent = progressState.stats.totalListings;
        document.getElementById('totalModifiers').textContent = progressState.stats.totalModifiers;
        document.getElementById('matchedModifiers').textContent = progressState.stats.matchedModifiers;
        document.getElementById('uniqueModifiers').textContent = progressState.stats.uniqueModifiers;

        // Show stats section if we have data
        if (progressState.stats.totalListings > 0) {
            document.getElementById('analysisStats').style.display = 'block';
        }
    }

    function addLogEntry(message) {
        progressState.logs.push(message);

        // Keep only last 10 entries
        if (progressState.logs.length > 10) {
            progressState.logs.shift();
        }

        // Update log display
        const logContainer = document.getElementById('logEntries');
        logContainer.innerHTML = progressState.logs.map(log =>
            `<div class="mb-1">${log}</div>`
        ).join('');

        // Show log section
        document.getElementById('activityLog').style.display = 'block';

        // Auto-scroll to bottom
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    function updateTabletProgress(tabletName, status, count = null) {
        progressState.tabletProgress[tabletName] = { status, count };

        // Show tablet progress section
        document.getElementById('tabletProgress').style.display = 'block';

        // Update tablet list
        const tabletList = document.getElementById('tabletList');
        const tabletItems = Object.entries(progressState.tabletProgress).map(([name, info]) => {
            const statusIcon = info.status === 'complete' ? 'bi-check-circle-fill text-success' :
                              info.status === 'error' ? 'bi-x-circle-fill text-danger' :
                              info.status === 'collecting' ? 'bi-arrow-clockwise spin text-warning' :
                              'bi-circle text-muted';

            const countText = info.count !== null ? ` (${info.count} listings)` : '';

            return `
                <div class="d-flex align-items-center mb-2">
                    <i class="bi ${statusIcon} me-2"></i>
                    <span class="text-light">${name}${countText}</span>
                </div>
            `;
        }).join('');

        tabletList.innerHTML = tabletItems;
    }

    function resetProgressState() {
        progressState = {
            currentStep: 'Initializing',
            overallProgress: 0,
            tabletProgress: {},
            stats: {
                totalListings: 0,
                totalModifiers: 0,
                matchedModifiers: 0,
                uniqueModifiers: 0
            },
            logs: []
        };

        // Hide optional sections
        document.getElementById('tabletProgress').style.display = 'none';
        document.getElementById('analysisStats').style.display = 'none';
        document.getElementById('activityLog').style.display = 'none';
    }
    
    function displayAnalysisResults(data) {
        // Show results section
        document.getElementById('resultsSection').style.display = 'block';

        // Display summary cards
        displaySummaryCards(data.summary);

        // Display tablet-based analysis
        displayTabletAnalysis(data.tablet_analysis);

        // Display synergies if any
        if (data.synergies && data.synergies.length > 0) {
            displaySynergies(data.synergies);
            document.getElementById('synergySection').style.display = 'block';
        }
        displayTabletBreakdowns(data.tablet_breakdowns);
    }
    
    function displaySummaryCards(summary) {
        const container = document.getElementById('summaryCards');
        container.innerHTML = `
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-warning">${summary.total_modifiers}</h3>
                        <p class="mb-0">Total Modifiers</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-success">${summary.valuable_modifiers}</h3>
                        <p class="mb-0">Valuable (10+ chaos)</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-gold">${summary.high_value_modifiers}</h3>
                        <p class="mb-0">High Priority</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-info">${summary.avg_confidence}%</h3>
                        <p class="mb-0">Avg Confidence</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-primary">${summary.total_listings}</h3>
                        <p class="mb-0">Total Listings</p>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card glass-effect">
                    <div class="card-body text-center">
                        <h3 class="text-secondary">${summary.tablet_types_analyzed}</h3>
                        <p class="mb-0">Tablet Types</p>
                    </div>
                </div>
            </div>
        `;
    }

    function displayTabletAnalysis(tabletAnalysis) {
        const container = document.getElementById('tabletAnalysisContainer');

        let html = '';

        // Create accordion for each tablet type
        Object.entries(tabletAnalysis).forEach(([tabletType, tabletData], index) => {
            const accordionId = `tablet-${index}`;
            const isFirst = index === 0;

            html += `
                <div class="accordion-item bg-dark border-secondary mb-3">
                    <h2 class="accordion-header" id="heading-${accordionId}">
                        <button class="accordion-button ${isFirst ? '' : 'collapsed'} bg-dark text-light" type="button"
                                data-bs-toggle="collapse" data-bs-target="#collapse-${accordionId}"
                                aria-expanded="${isFirst}" aria-controls="collapse-${accordionId}">
                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                <span class="fw-bold">${tabletType}</span>
                                <div class="d-flex gap-3">
                                    <span class="badge bg-warning text-dark">${tabletData.total_modifiers} modifiers</span>
                                    <span class="badge bg-info">${tabletData.total_listings} listings</span>
                                    <span class="badge bg-success">${tabletData.avg_price_exalt} ex avg</span>
                                </div>
                            </div>
                        </button>
                    </h2>
                    <div id="collapse-${accordionId}" class="accordion-collapse collapse ${isFirst ? 'show' : ''}"
                         aria-labelledby="heading-${accordionId}">
                        <div class="accordion-body">
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <div class="card bg-secondary border-warning">
                                        <div class="card-body text-center p-2">
                                            <div class="h6 mb-1 text-warning">${tabletData.valuable_modifiers}</div>
                                            <small class="text-muted">Valuable (>0.05ex)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary border-warning">
                                        <div class="card-body text-center p-2">
                                            <div class="h6 mb-1 text-info">${tabletData.high_value_modifiers}</div>
                                            <small class="text-muted">High Priority</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary border-warning">
                                        <div class="card-body text-center p-2">
                                            <div class="h6 mb-1 text-success">${tabletData.avg_confidence}</div>
                                            <small class="text-muted">Avg Confidence</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-secondary border-warning">
                                        <div class="card-body text-center p-2">
                                            <div class="h6 mb-1 text-primary">${tabletData.avg_price_chaos.toFixed(0)}c</div>
                                            <small class="text-muted">Avg Price</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-dark table-striped table-hover table-sm">
                                    <thead>
                                        <tr>
                                            <th style="width: 35%">Modifier</th>
                                            <th style="width: 8%">Tier</th>
                                            <th style="width: 12%">Median (ex)</th>
                                            <th style="width: 12%">Range (ex)</th>
                                            <th style="width: 8%">Synergy</th>
                                            <th style="width: 8%">Confidence</th>
                                            <th style="width: 6%">Listings</th>
                                            <th style="width: 11%">Sample Values</th>
                                        </tr>
                                    </thead>
                                    <tbody>
            `;

            // Add modifier rows
            tabletData.modifiers.forEach(modifier => {
                const tierBadge = getTierBadge(modifier.tier);
                const synergyBadge = getSynergyBadge(modifier.synergy_rate);
                const confidenceBadge = getConfidenceBadge(modifier.confidence);
                const sampleValues = modifier.sample_values.slice(0, 3).join(', ');

                html += `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                ${modifier.is_high_value ? '<i class="bi bi-star-fill text-warning me-1"></i>' : ''}
                                <span class="small">${modifier.name}</span>
                            </div>
                        </td>
                        <td>${tierBadge}</td>
                        <td class="text-warning fw-bold">${modifier.median_price_exalt}</td>
                        <td class="small">${modifier.min_price_exalt} - ${modifier.max_price_exalt}</td>
                        <td>${synergyBadge}</td>
                        <td>${confidenceBadge}</td>
                        <td>${modifier.listings}</td>
                        <td class="small text-muted">${sampleValues}</td>
                    </tr>
                `;
            });

            html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = `<div class="accordion" id="tabletAccordion">${html}</div>`;
    }

    function getTierBadge(tier) {
        const badges = {
            'High': '<span class="badge bg-success">High</span>',
            'Mid': '<span class="badge bg-warning text-dark">Mid</span>',
            'Low': '<span class="badge bg-secondary">Low</span>',
            'Unknown': '<span class="badge bg-dark">?</span>'
        };
        return badges[tier] || badges['Unknown'];
    }

    function getSynergyBadge(rate) {
        if (rate >= 0.5) return '<span class="badge bg-success">High</span>';
        if (rate >= 0.2) return '<span class="badge bg-warning text-dark">Med</span>';
        if (rate > 0) return '<span class="badge bg-secondary">Low</span>';
        return '<span class="badge bg-dark">None</span>';
    }

    function getConfidenceBadge(confidence) {
        if (confidence >= 0.8) return '<span class="badge bg-success">' + Math.round(confidence * 100) + '%</span>';
        if (confidence >= 0.6) return '<span class="badge bg-warning text-dark">' + Math.round(confidence * 100) + '%</span>';
        if (confidence >= 0.4) return '<span class="badge bg-secondary">' + Math.round(confidence * 100) + '%</span>';
        return '<span class="badge bg-danger">' + Math.round(confidence * 100) + '%</span>';
    }

    function displayModifierTable(modifiers) {
        sortAndDisplayModifiers('value');
    }
    
    function sortAndDisplayModifiers(sortBy) {
        if (!currentAnalysisData || !currentAnalysisData.modifiers) return;
        
        let modifiers = [...currentAnalysisData.modifiers];
        
        // Sort modifiers
        modifiers.sort((a, b) => {
            switch(sortBy) {
                case 'value':
                    return b.value - a.value;
                case 'confidence':
                    return b.confidence - a.confidence;
                case 'name':
                    return a.text.localeCompare(b.text);
                default:
                    return b.value - a.value;
            }
        });
        
        const tableBody = document.getElementById('modifierTable');
        tableBody.innerHTML = '';
        
        modifiers.forEach(modifier => {
            const row = document.createElement('tr');
            row.className = 'fade-in';
            
            const valueClass = getValueClass(modifier.value);
            const confidenceClass = getConfidenceClass(modifier.confidence);
            
            row.innerHTML = `
                <td>
                    <div>
                        <span class="modifier-explicit">${modifier.text}</span>
                        ${modifier.is_high_value ? '<span class="badge bg-warning ms-1">High Value</span>' : ''}
                    </div>
                    <small class="text-muted">${modifier.name} (${modifier.mod_type})</small>
                </td>
                <td>
                    <span class="${valueClass}">
                        ${formatPrice(modifier.value, modifier.currency)}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${modifier.price_range} chaos</small>
                </td>
                <td>
                    <span class="badge ${confidenceClass}">
                        ${(modifier.confidence * 100).toFixed(0)}%
                    </span>
                </td>
                <td>${modifier.listings}</td>
                <td>
                    <small class="text-muted">
                        ${modifier.tablet_types.slice(0, 2).join(', ')}
                        ${modifier.tablet_types.length > 2 ? ` +${modifier.tablet_types.length - 2} more` : ''}
                    </small>
                </td>
            `;
            
            tableBody.appendChild(row);
        });
    }
    
    function displaySynergies(synergies) {
        const container = document.getElementById('synergyContent');
        let html = '';
        
        synergies.forEach(synergy => {
            html += `
                <div class="card mb-3 glass-effect">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="text-warning">Synergy Combination</h6>
                                <ul class="list-unstyled mb-2">
                                    ${synergy.modifiers.map(mod => `<li><small class="modifier-explicit">${mod}</small></li>`).join('')}
                                </ul>
                            </div>
                            <div class="col-md-4 text-end">
                                <h6 class="text-success">+${formatPrice(synergy.bonus_value, synergy.currency)} bonus</h6>
                                <small class="text-muted">${synergy.occurrences} occurrences</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    function displayTabletBreakdowns(breakdowns) {
        const container = document.getElementById('tabletAccordion');
        let html = '';
        
        Object.entries(breakdowns).forEach(([tabletType, data], index) => {
            const collapseId = `collapse${index}`;
            html += `
                <div class="accordion-item bg-dark border-secondary">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed bg-dark text-light border-0" type="button" 
                                data-bs-toggle="collapse" data-bs-target="#${collapseId}">
                            <strong>${tabletType}</strong>
                            <span class="badge bg-warning ms-2">${data.total_listings} listings</span>
                        </button>
                    </h2>
                    <div id="${collapseId}" class="accordion-collapse collapse" data-bs-parent="#tabletAccordion">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-gold">Top Modifiers</h6>
                                    <ul class="list-unstyled">
                                        ${data.top_modifiers.slice(0, 5).map(mod => 
                                            `<li><small class="modifier-explicit">${mod.text}</small> - 
                                             <span class="${getValueClass(mod.value)}">${formatPrice(mod.value, mod.currency)}</span></li>`
                                        ).join('')}
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-gold">Statistics</h6>
                                    <ul class="list-unstyled">
                                        <li><strong>Avg Price:</strong> ${formatPrice(data.avg_price, data.currency)}</li>
                                        <li><strong>Data Quality:</strong> ${data.data_quality}</li>
                                        <li><strong>Unique Modifiers:</strong> ${data.unique_modifiers}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }
    
    function getValueClass(value) {
        if (value >= 50) return 'modifier-value-high';
        if (value >= 10) return 'modifier-value-medium';
        if (value > 0) return 'modifier-value-low';
        return 'modifier-value-none';
    }
    
    function getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'bg-success';
        if (confidence >= 0.6) return 'bg-warning';
        if (confidence >= 0.4) return 'bg-info';
        return 'bg-danger';
    }

    // Regex generator button handler
    document.getElementById('generateRegexBtn').addEventListener('click', async function() {
        const minValue = parseFloat(document.getElementById('minValue').value);
        const maxPatterns = parseInt(document.getElementById('maxPatterns').value);

        // Show loading state
        const btn = this;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="bi bi-hourglass-split"></i> Generating...';
        btn.disabled = true;

        try {
            const response = await fetch('/api/generate-regex', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    min_value: minValue,
                    max_patterns: maxPatterns
                })
            });

            const data = await response.json();

            if (data.success) {
                displayRegexResults(data);
                document.getElementById('regexSection').style.display = 'block';
                showToast('Regex patterns generated successfully!', 'success');
            } else {
                showToast('Error generating regex patterns: ' + data.error, 'error');
            }
        } catch (error) {
            console.error('Error:', error);
            showToast('Failed to generate regex patterns', 'error');
        } finally {
            // Reset button
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    });

    function displayRegexResults(data) {
        const container = document.getElementById('regexResults');

        if (!data.patterns || data.patterns.length === 0) {
            container.innerHTML = '<div class="alert alert-warning">No regex patterns generated.</div>';
            return;
        }

        let html = `
            <div class="alert alert-info mb-4">
                <i class="bi bi-info-circle"></i>
                <strong>Usage:</strong> Copy these regex patterns into your PoE stash search to find tablets with valuable modifiers.
                Each pattern is under 50 characters to fit the game's search limit.
            </div>
        `;

        data.patterns.forEach((pattern, index) => {
            const copyId = `copy-${index}`;
            html += `
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">${pattern.name}</h6>
                                <small class="text-muted">${pattern.description}</small>
                            </div>
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control font-monospace"
                                           value="${pattern.pattern}" readonly id="${copyId}">
                                    <button class="btn btn-outline-secondary" type="button"
                                            onclick="copyToClipboard('${copyId}')">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <span class="badge ${pattern.length <= 40 ? 'bg-success' : pattern.length <= 50 ? 'bg-warning' : 'bg-danger'}">
                                    ${pattern.length}/50 chars
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    function copyToClipboard(elementId) {
        const element = document.getElementById(elementId);
        element.select();
        element.setSelectionRange(0, 99999); // For mobile devices

        try {
            document.execCommand('copy');
            showToast('Regex pattern copied to clipboard!', 'success');
        } catch (err) {
            showToast('Failed to copy to clipboard', 'error');
        }
    }
</script>
{% endblock %}
