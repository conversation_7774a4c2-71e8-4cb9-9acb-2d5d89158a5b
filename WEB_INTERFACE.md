# 🌐 PoE 2 Trade Valuation - Web Interface

A beautiful, user-friendly web interface for analyzing Path of Exile 2 item values and modifier prices.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure Your POESESSID**
   - Run: `python -m poe_trade.cli.main init-config`
   - Edit `config/config.yaml` and set your POESESSID
   - Get your POESESSID from pathofexile.com cookies

3. **Launch the Web Interface**
   ```bash
   python run_web.py
   ```

4. **Open Your Browser**
   - The interface will automatically open at `http://127.0.0.1:5000`
   - If it doesn't open automatically, navigate there manually

## ✨ Features

### 🎨 Beautiful Design
- **PoE-themed styling** with gold accents and dark backgrounds
- **Responsive design** that works on desktop and mobile
- **Smooth animations** and modern UI components
- **Glass morphism effects** for a premium feel

### 🔍 Item Search
- **Real-time search** for any item type
- **League selection** from available leagues
- **Detailed results** showing item properties, modifiers, and prices
- **Seller information** with online status indicators
- **Rarity-based color coding** (Normal, Magic, Rare, Unique)

### 📊 Market Analysis
- **Comprehensive market analysis** for any item type
- **Modifier valuation** with confidence scores
- **Base price calculation** using median values
- **Data quality indicators** to assess reliability
- **Sample size configuration** for thorough analysis

### 💎 Precursor Tablet Specialization
- **Dedicated precursor tablet analysis** across all types
- **Comparative analysis** between different tablet types
- **Valuable modifier identification** for profitable trading
- **Batch processing** for efficiency

### 🌐 Configuration Management
- **Visual configuration errors** with helpful instructions
- **Connection testing** with real-time feedback
- **Toast notifications** for user feedback
- **Automatic error handling** and recovery

## 🎯 Perfect for Your Use Case

The web interface is specifically designed for your precursor tablet valuation needs:

1. **Quick Analysis**: Click "Analyze All Precursor Tablets" for instant market overview
2. **Detailed Search**: Search specific tablet types to see current listings
3. **Modifier Values**: Identify which modifiers are currently valuable
4. **Market Trends**: Track price changes and market conditions

## 🛠️ Technical Details

### Architecture
- **Flask backend** with RESTful API endpoints
- **Bootstrap 5** for responsive UI components
- **Vanilla JavaScript** for lightweight client-side interactions
- **Real-time data** from PoE 2 trade API

### API Endpoints
- `GET /` - Main dashboard
- `GET /api/leagues` - Available leagues
- `GET /api/test-connection` - Connection testing
- `POST /api/search` - Item search
- `POST /api/analyze` - Market analysis
- `POST /api/precursor-analysis` - Precursor tablet analysis

### Security
- **Local-only access** (127.0.0.1) for security
- **No external data storage** - all data stays on your machine
- **Session-based authentication** using your POESESSID

## 🎨 UI Components

### Dashboard
- **Welcome section** with tool overview
- **Search form** for item queries
- **Analysis form** for market analysis
- **Results display** with interactive tables

### Search Results
- **Sortable tables** with item details
- **Modifier tooltips** for detailed information
- **Price formatting** with currency symbols
- **Online status indicators** for sellers

### Analysis Results
- **Data quality metrics** with visual indicators
- **Valuable modifier lists** sorted by value
- **Confidence scores** for reliability assessment
- **Summary statistics** for quick overview

## 🔧 Customization

### Styling
The interface uses CSS custom properties for easy theming:
```css
:root {
    --poe-gold: #c9aa71;
    --poe-dark: #1a1a1a;
    --poe-blue: #4a90e2;
    /* ... more variables */
}
```

### Configuration
Modify `poe_trade/web/app.py` to:
- Change default settings
- Add new API endpoints
- Customize error handling
- Adjust rate limiting

## 🐛 Troubleshooting

### Common Issues

**"Configuration Error" on startup:**
- Run `python -m poe_trade.cli.main init-config`
- Edit `config/config.yaml` with your POESESSID
- Restart the web interface

**"Connection failed" errors:**
- Verify your POESESSID is correct and current
- Check your internet connection
- Ensure you're logged into pathofexile.com

**No search results:**
- Try different item type names
- Check if the league is correct
- Verify items exist in the selected league

**Slow performance:**
- Reduce max results in search/analysis
- Check your internet connection speed
- Consider using fewer concurrent requests

### Getting Help

1. Check the browser console for JavaScript errors
2. Look at the Flask server logs in the terminal
3. Verify your configuration in `config/config.yaml`
4. Test the CLI interface to isolate issues

## 🚀 Future Enhancements

Planned features for future versions:
- **Real-time price alerts** for valuable items
- **Historical price charts** and trends
- **Advanced filtering** and search options
- **Export functionality** for analysis results
- **User preferences** and saved searches
- **Dark/light theme toggle**
- **Mobile app** version

---

**Enjoy trading with confidence!** 💎✨
