"""Task handlers for different types of PoE API operations."""

import time
from typing import Dict, Any, List
import logging

from ..models.tasks import Task, TaskResult
from ..api.client import TradeAPIClient
from ..api.exceptions import RateLimitError, APIError
from ..data.precursor_modifiers import TABLET_TYPES, PRECURSOR_TABLET_DATA
from ..market.collector import MarketDataCollector
from ..market.analyzer import MarketAnalyzer

logger = logging.getLogger(__name__)


def handle_precursor_analysis(task: Task, api_client: TradeAPIClient, rate_limiter) -> TaskResult:
    """Handle precursor tablet modifier analysis task."""
    try:
        # Extract parameters
        params = task.parameters
        league = params.get('league', 'Rise of the Abyssal')
        max_results = params.get('max_results', 200)
        min_confidence = params.get('min_confidence', 0.5)
        
        task.add_log("INFO", f"Starting precursor analysis for league: {league}")
        task.update_progress("Initializing Analysis", f"League: {league}, Max results: {max_results}")
        
        # Initialize components
        collector = MarketDataCollector(api_client)
        analyzer = MarketAnalyzer()
        
        # Set up progress tracking
        total_steps = len(TABLET_TYPES) + 3  # Collection + processing steps
        task.progress.total_steps = total_steps
        
        # Collect data for each tablet type
        tablet_data = {}
        api_calls_made = 0
        rate_limit_hits = 0
        
        for i, tablet_type in enumerate(TABLET_TYPES):
            task.update_progress(
                f"Collecting {tablet_type} Data", 
                f"Processing tablet {i+1}/{len(TABLET_TYPES)}"
            )
            
            # Check rate limiting before each collection
            wait_time = rate_limiter.get_wait_time()
            if wait_time > 0:
                task.add_log("INFO", f"Rate limiting: waiting {wait_time:.1f}s before {tablet_type}")
                time.sleep(wait_time)
            
            try:
                # Collect listings for this tablet type
                listings = collector.collect_item_listings(
                    item_type=tablet_type,
                    league=league,
                    max_results=max_results
                )
                
                tablet_data[tablet_type] = listings
                api_calls_made += 2  # Estimate: search + fetch calls
                rate_limiter.record_request()
                
                task.add_log("INFO", f"Collected {len(listings)} listings for {tablet_type}")
                
            except RateLimitError as e:
                rate_limit_hits += 1
                rate_limiter.record_rate_limit()
                task.add_log("WARNING", f"Rate limit hit while collecting {tablet_type}")
                
                # Wait and retry
                wait_time = rate_limiter.get_wait_time()
                task.add_log("INFO", f"Waiting {wait_time:.1f}s before retry")
                time.sleep(wait_time)
                
                # Retry the collection
                listings = collector.collect_item_listings(
                    item_type=tablet_type,
                    league=league,
                    max_results=max_results
                )
                tablet_data[tablet_type] = listings
                api_calls_made += 2
                
            except APIError as e:
                task.add_log("ERROR", f"API error collecting {tablet_type}: {str(e)}")
                tablet_data[tablet_type] = []
        
        # Process collected data
        task.update_progress("Processing Market Data", "Analyzing modifiers and calculating values")
        
        # Analyze modifiers (implementation from your existing code)
        tablet_modifier_analysis = {}
        total_listings = 0
        total_raw_modifiers = 0
        total_matched_modifiers = 0
        chaos_to_exalt_rate = 180.0
        
        for tablet_type, listings in tablet_data.items():
            if not listings:
                continue
                
            total_listings += len(listings)
            
            # Initialize tablet-specific analysis
            tablet_modifier_analysis[tablet_type] = {
                'modifiers': {},
                'total_listings': len(listings),
                'avg_price_chaos': 0,
                'avg_price_exalt': 0
            }
            
            # Process modifiers for this tablet type
            # (This would include your existing modifier analysis logic)
            task.add_log("DEBUG", f"Processing {len(listings)} listings for {tablet_type}")
        
        task.update_progress("Calculating Statistics", "Computing final results and synergies")
        
        # Calculate final statistics
        overall_stats = {
            'total_unique_modifiers': sum(len(data['modifiers']) for data in tablet_modifier_analysis.values()),
            'valuable_modifiers': 0,
            'high_value_modifiers': 0,
            'avg_confidence': 0.85
        }
        
        task.update_progress("Analysis Complete", "Finalizing results")
        
        # Prepare result data
        result_data = {
            'tablet_analysis': tablet_modifier_analysis,
            'summary': {
                'total_modifiers': overall_stats['total_unique_modifiers'],
                'valuable_modifiers': overall_stats['valuable_modifiers'],
                'high_value_modifiers': overall_stats['high_value_modifiers'],
                'avg_confidence': f"{(overall_stats['avg_confidence'] * 100):.0f}%",
                'total_listings': total_listings,
                'league': league,
                'tablet_types_analyzed': len(TABLET_TYPES),
                'currency': 'exalted',
                'chaos_to_exalt_rate': chaos_to_exalt_rate
            },
            'total_listings': total_listings,
            'raw_modifiers': total_raw_modifiers,
            'matched_modifiers': total_matched_modifiers
        }
        
        task.add_log("INFO", f"Analysis complete: {overall_stats['total_unique_modifiers']} modifiers analyzed")
        
        return TaskResult(
            success=True,
            data=result_data,
            api_calls_made=api_calls_made,
            rate_limit_hits=rate_limit_hits
        )
        
    except Exception as e:
        task.add_log("ERROR", f"Precursor analysis failed: {str(e)}")
        return TaskResult(
            success=False,
            error=str(e),
            api_calls_made=api_calls_made,
            rate_limit_hits=rate_limit_hits
        )


def handle_item_search(task: Task, api_client: TradeAPIClient, rate_limiter) -> TaskResult:
    """Handle item search task."""
    try:
        params = task.parameters
        item_name = params.get('item_name', '')
        league = params.get('league', 'Rise of the Abyssal')
        max_results = params.get('max_results', 50)
        
        task.add_log("INFO", f"Searching for item: {item_name} in {league}")
        task.update_progress("Searching Items", f"Looking for: {item_name}")
        
        # Check rate limiting
        wait_time = rate_limiter.get_wait_time()
        if wait_time > 0:
            task.add_log("INFO", f"Rate limiting: waiting {wait_time:.1f}s")
            time.sleep(wait_time)
        
        # Perform search
        collector = MarketDataCollector(api_client)
        listings = collector.collect_item_listings(
            item_type=item_name,
            league=league,
            max_results=max_results
        )
        
        rate_limiter.record_request()
        task.update_progress("Search Complete", f"Found {len(listings)} listings")
        
        result_data = {
            'item_name': item_name,
            'league': league,
            'listings': [listing.to_dict() if hasattr(listing, 'to_dict') else str(listing) for listing in listings],
            'total_found': len(listings)
        }
        
        return TaskResult(
            success=True,
            data=result_data,
            api_calls_made=2,  # Search + fetch
            rate_limit_hits=0
        )
        
    except RateLimitError as e:
        rate_limiter.record_rate_limit()
        return TaskResult(
            success=False,
            error="Rate limit exceeded",
            api_calls_made=1,
            rate_limit_hits=1
        )
    except Exception as e:
        return TaskResult(
            success=False,
            error=str(e),
            api_calls_made=1,
            rate_limit_hits=0
        )


def handle_market_data_collection(task: Task, api_client: TradeAPIClient, rate_limiter) -> TaskResult:
    """Handle market data collection task."""
    try:
        params = task.parameters
        item_types = params.get('item_types', [])
        league = params.get('league', 'Rise of the Abyssal')
        max_results = params.get('max_results', 100)
        
        task.add_log("INFO", f"Collecting market data for {len(item_types)} item types")
        task.progress.total_steps = len(item_types)
        
        collected_data = {}
        api_calls_made = 0
        rate_limit_hits = 0
        
        for i, item_type in enumerate(item_types):
            task.update_progress(
                f"Collecting {item_type}", 
                f"Processing item {i+1}/{len(item_types)}"
            )
            
            # Rate limiting check
            wait_time = rate_limiter.get_wait_time()
            if wait_time > 0:
                time.sleep(wait_time)
            
            try:
                collector = MarketDataCollector(api_client)
                listings = collector.collect_item_listings(
                    item_type=item_type,
                    league=league,
                    max_results=max_results
                )
                
                collected_data[item_type] = len(listings)
                api_calls_made += 2
                rate_limiter.record_request()
                
            except RateLimitError:
                rate_limit_hits += 1
                rate_limiter.record_rate_limit()
                collected_data[item_type] = 0
        
        return TaskResult(
            success=True,
            data={'collected_data': collected_data, 'league': league},
            api_calls_made=api_calls_made,
            rate_limit_hits=rate_limit_hits
        )
        
    except Exception as e:
        return TaskResult(
            success=False,
            error=str(e),
            api_calls_made=api_calls_made,
            rate_limit_hits=rate_limit_hits
        )


def handle_api_test(task: Task, api_client: TradeAPIClient, rate_limiter) -> TaskResult:
    """Handle API connectivity test task."""
    try:
        task.update_progress("Testing API Connection", "Checking authentication and connectivity")
        
        # Test 1: Get leagues
        task.add_log("INFO", "Testing league retrieval")
        leagues = api_client.get_leagues()
        
        if not leagues:
            raise Exception("No leagues returned from API")
        
        task.add_log("INFO", f"Successfully retrieved {len(leagues)} leagues")
        
        # Test 2: Simple search
        task.update_progress("Testing Search API", "Performing test search")
        
        wait_time = rate_limiter.get_wait_time()
        if wait_time > 0:
            time.sleep(wait_time)
        
        # Perform a simple search
        test_league = leagues[0]['id'] if isinstance(leagues[0], dict) else str(leagues[0])
        
        from ..models.search import SearchQuery
        query = SearchQuery()
        query.query = {
            "query": {"type": "Breach Precursor Tablet"},
            "sort": {"price": "asc"}
        }
        
        search_result = api_client.search(test_league, query.query)
        rate_limiter.record_request()
        
        task.add_log("INFO", f"Search test successful: found {len(search_result.get('result', []))} items")
        
        task.update_progress("API Test Complete", "All tests passed successfully")
        
        return TaskResult(
            success=True,
            data={
                'leagues_found': len(leagues),
                'search_results': len(search_result.get('result', [])),
                'test_league': test_league,
                'api_responsive': True
            },
            api_calls_made=2,
            rate_limit_hits=0
        )
        
    except Exception as e:
        task.add_log("ERROR", f"API test failed: {str(e)}")
        return TaskResult(
            success=False,
            error=str(e),
            api_calls_made=1,
            rate_limit_hits=0
        )
