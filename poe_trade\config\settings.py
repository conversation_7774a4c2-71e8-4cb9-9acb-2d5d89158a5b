"""
Configuration management for the PoE 2 trade valuation tool.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field, validator


class APIConfig(BaseModel):
    """API configuration settings."""
    
    poesessid: str
    base_url: str = "https://www.pathofexile.com/api/trade2/"
    rate_limit: int = 60
    timeout: int = 30
    user_agent: str = "PoE2 Trade Valuation Tool/0.1.0"
    
    @validator('poesessid')
    def validate_poesessid(cls, v):
        """Validate POESESSID format."""
        if not v or v == "YOUR_POESESSID_HERE":
            raise ValueError("POESESSID must be set to a valid session ID")
        return v


class LeagueConfig(BaseModel):
    """League configuration settings."""
    
    default: str = "Standard"
    available: List[str] = Field(default_factory=lambda: [
        "Standard", "Hardcore", "Rise of the Abyssal", "HC Rise of the Abyssal"
    ])


class SearchConfig(BaseModel):
    """Search configuration settings."""
    
    default_limit: int = 100
    max_results: int = 1000
    online_only: bool = True
    currency: Optional[str] = "exalted"


class ValuationConfig(BaseModel):
    """Valuation configuration settings."""
    
    min_listings: int = 5
    price_percentile: int = 50
    time_decay_factor: float = 0.1
    min_confidence: float = 0.6
    
    @validator('price_percentile')
    def validate_percentile(cls, v):
        """Validate percentile is between 0 and 100."""
        if not 0 <= v <= 100:
            raise ValueError("Price percentile must be between 0 and 100")
        return v
    
    @validator('min_confidence')
    def validate_confidence(cls, v):
        """Validate confidence is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Minimum confidence must be between 0 and 1")
        return v


class CacheConfig(BaseModel):
    """Cache configuration settings."""
    
    directory: str = "cache"
    market_data_expiry: int = 60  # minutes
    modifier_values_expiry: int = 1440  # 24 hours in minutes
    max_size: int = 500  # MB
    
    @property
    def cache_path(self) -> Path:
        """Get the cache directory path."""
        return Path(self.directory)


class DatabaseConfig(BaseModel):
    """Database configuration settings."""
    
    path: str = "data/poe_trade.db"
    wal_mode: bool = True
    
    @property
    def db_path(self) -> Path:
        """Get the database file path."""
        return Path(self.path)


class LoggingConfig(BaseModel):
    """Logging configuration settings."""
    
    level: str = "INFO"
    file: str = "logs/poe_trade.log"
    console: bool = True
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    @property
    def log_path(self) -> Path:
        """Get the log file path."""
        return Path(self.file)


class CLIConfig(BaseModel):
    """CLI configuration settings."""
    
    output_format: str = "table"
    colored: bool = True
    show_progress: bool = True
    
    @validator('output_format')
    def validate_output_format(cls, v):
        """Validate output format."""
        valid_formats = ["table", "json", "csv"]
        if v not in valid_formats:
            raise ValueError(f"Output format must be one of: {valid_formats}")
        return v


class Settings(BaseModel):
    """Main settings container."""
    
    api: APIConfig
    league: LeagueConfig = Field(default_factory=LeagueConfig)
    search: SearchConfig = Field(default_factory=SearchConfig)
    valuation: ValuationConfig = Field(default_factory=ValuationConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    cli: CLIConfig = Field(default_factory=CLIConfig)
    
    def ensure_directories(self):
        """Ensure all required directories exist."""
        directories = [
            self.cache.cache_path,
            self.database.db_path.parent,
            self.logging.log_path.parent
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


def load_config(config_path: Optional[str] = None) -> Settings:
    """
    Load configuration from YAML file.
    
    Args:
        config_path: Path to config file. If None, searches for config.yaml
        
    Returns:
        Settings object
        
    Raises:
        FileNotFoundError: If config file not found
        ValueError: If config is invalid
    """
    if config_path is None:
        # Search for config file in common locations
        search_paths = [
            Path("config/config.yaml"),
            Path("config.yaml"),
            Path.home() / ".poe_trade" / "config.yaml",
        ]
        
        config_path = None
        for path in search_paths:
            if path.exists():
                config_path = path
                break
        
        if config_path is None:
            raise FileNotFoundError(
                "Config file not found. Please create config/config.yaml or "
                "copy from config/config.example.yaml"
            )
    else:
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
    
    # Load YAML config
    with open(config_path, 'r', encoding='utf-8') as f:
        config_data = yaml.safe_load(f)
    
    # Override with environment variables if present
    if 'POESESSID' in os.environ:
        if 'api' not in config_data:
            config_data['api'] = {}
        config_data['api']['poesessid'] = os.environ['POESESSID']
    
    # Create settings object
    try:
        settings = Settings(**config_data)
        settings.ensure_directories()
        return settings
    except Exception as e:
        raise ValueError(f"Invalid configuration: {e}")


def create_example_config(output_path: str = "config/config.yaml"):
    """Create an example configuration file."""
    example_config = {
        'api': {
            'poesessid': 'YOUR_POESESSID_HERE',
            'base_url': 'https://www.pathofexile.com/api/trade2/',
            'rate_limit': 60,
            'timeout': 30,
            'user_agent': 'PoE2 Trade Valuation Tool/0.1.0'
        },
        'league': {
            'default': 'Standard',
            'available': ['Standard', 'Hardcore', 'Rise of the Abyssal', 'HC Rise of the Abyssal']
        },
        'search': {
            'default_limit': 100,
            'max_results': 1000,
            'online_only': True,
            'currency': 'exalted'
        },
        'valuation': {
            'min_listings': 5,
            'price_percentile': 50,
            'time_decay_factor': 0.1,
            'min_confidence': 0.6
        },
        'cache': {
            'directory': 'cache',
            'market_data_expiry': 60,
            'modifier_values_expiry': 1440,
            'max_size': 500
        },
        'database': {
            'path': 'data/poe_trade.db',
            'wal_mode': True
        },
        'logging': {
            'level': 'INFO',
            'file': 'logs/poe_trade.log',
            'console': True,
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'cli': {
            'output_format': 'table',
            'colored': True,
            'show_progress': True
        }
    }
    
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        yaml.dump(example_config, f, default_flow_style=False, indent=2)
