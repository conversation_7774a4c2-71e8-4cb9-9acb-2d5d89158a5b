"""
Historical market data collector that integrates with the storage system.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from .database import DatabaseManager
from .cache import CacheManager
from .models import MarketSnapshot, ModifierTrend, HistoricalMarketData
from .trend_analyzer import TrendAnalyzer
from ..models import ItemListing, MarketData, ModifierValue, Currency
from ..market import MarketDataCollector, MarketAnalyzer


logger = logging.getLogger(__name__)


class HistoricalMarketCollector:
    """Collects and stores historical market data with trend analysis."""
    
    def __init__(
        self,
        market_collector: MarketDataCollector,
        market_analyzer: MarketAnalyzer,
        database_manager: DatabaseManager,
        cache_manager: CacheManager
    ):
        """
        Initialize historical market collector.
        
        Args:
            market_collector: Market data collector instance
            market_analyzer: Market analyzer instance
            database_manager: Database manager for persistence
            cache_manager: Cache manager for performance
        """
        self.market_collector = market_collector
        self.market_analyzer = market_analyzer
        self.db = database_manager
        self.cache = cache_manager
        self.trend_analyzer = TrendAnalyzer()
        
        logger.info("Historical market collector initialized")
    
    def collect_and_store_market_data(
        self,
        item_type: str,
        league: str,
        max_results: int = 200
    ) -> MarketSnapshot:
        """
        Collect current market data and store it historically.
        
        Args:
            item_type: Type of item to collect data for
            league: League to collect data from
            max_results: Maximum number of listings to collect
            
        Returns:
            MarketSnapshot of the collected data
        """
        logger.info(f"Collecting and storing market data for {item_type} in {league}")
        
        # Collect current market listings
        listings = self.market_collector.collect_item_listings(
            item_type=item_type,
            league=league,
            max_results=max_results
        )
        
        if not listings:
            logger.warning(f"No listings found for {item_type} in {league}")
            return self._create_empty_snapshot(item_type, league)
        
        # Analyze the market data
        market_data = self.market_analyzer.analyze_market_data(
            item_type=item_type,
            league=league,
            listings=listings
        )
        
        # Create market snapshot
        snapshot = self._create_market_snapshot(market_data, listings)
        
        # Store snapshot in database
        snapshot_id = self.db.store_market_snapshot(snapshot)
        snapshot.id = snapshot_id
        
        # Store modifier trends
        self._store_modifier_trends(market_data, listings)
        
        # Cache the snapshot
        self.cache.cache_market_snapshots(item_type, league, [snapshot])
        
        logger.info(f"Stored market snapshot {snapshot_id} for {item_type}")
        return snapshot
    
    def _create_market_snapshot(
        self,
        market_data: MarketData,
        listings: List[ItemListing]
    ) -> MarketSnapshot:
        """Create a market snapshot from analyzed market data."""
        # Calculate price statistics
        prices = []
        for listing in listings:
            if listing.price_value is not None:
                prices.append(listing.price_value)
        
        min_price = min(prices) if prices else None
        max_price = max(prices) if prices else None
        
        # Calculate listing age statistics
        listing_ages = []
        now = datetime.now()
        for listing in listings:
            if hasattr(listing, 'indexed_at') and listing.indexed_at:
                age_hours = (now - listing.indexed_at).total_seconds() / 3600
                listing_ages.append(age_hours)
        
        avg_listing_age = sum(listing_ages) / len(listing_ages) if listing_ages else None
        
        # Calculate market activity score
        activity_score = self._calculate_market_activity_score(listings)
        
        # Calculate price volatility
        volatility = None
        if len(prices) > 1:
            import statistics
            mean_price = statistics.mean(prices)
            if mean_price > 0:
                std_dev = statistics.stdev(prices)
                volatility = std_dev / mean_price
        
        return MarketSnapshot(
            timestamp=datetime.now(),
            item_type=market_data.item_type,
            league=market_data.league,
            base_price=market_data.base_price.amount if market_data.base_price else None,
            currency=market_data.base_price.currency if market_data.base_price else None,
            median_price=market_data.base_price.amount if market_data.base_price else None,
            min_price=min_price,
            max_price=max_price,
            total_listings=market_data.total_listings,
            active_listings=len([l for l in listings if getattr(l, 'account_online', True)]),
            average_listing_age_hours=avg_listing_age,
            data_quality_score=market_data.data_quality,
            sample_size=len(listings),
            confidence_score=market_data.base_price.confidence if market_data.base_price else 0.0,
            price_volatility=volatility,
            market_activity_score=activity_score
        )
    
    def _store_modifier_trends(
        self,
        market_data: MarketData,
        listings: List[ItemListing]
    ):
        """Store modifier trend data."""
        for modifier_id, modifier_value in market_data.modifier_values.items():
            trend = ModifierTrend(
                modifier_id=modifier_id,
                modifier_text=modifier_value.modifier_text,
                item_type=market_data.item_type,
                league=market_data.league,
                timestamp=datetime.now(),
                base_value=modifier_value.base_value.amount,
                currency=modifier_value.base_value.currency,
                value_per_tier=modifier_value.value_per_tier.amount if modifier_value.value_per_tier else None,
                value_per_point=modifier_value.value_per_point.amount if modifier_value.value_per_point else None,
                min_value=modifier_value.min_price.amount if modifier_value.min_price else None,
                max_value=modifier_value.max_price.amount if modifier_value.max_price else None,
                median_value=modifier_value.median_price.amount if modifier_value.median_price else None,
                total_listings=modifier_value.total_listings,
                sample_size=modifier_value.recent_sales,
                confidence_score=modifier_value.base_value.confidence
            )
            
            self.db.store_modifier_trend(trend)
    
    def _calculate_market_activity_score(self, listings: List[ItemListing]) -> float:
        """Calculate a market activity score based on various factors."""
        if not listings:
            return 0.0
        
        factors = []
        
        # Factor 1: Number of listings (normalized)
        listing_count_score = min(1.0, len(listings) / 100.0)
        factors.append(listing_count_score)
        
        # Factor 2: Price diversity (coefficient of variation)
        prices = [l.price_value for l in listings if l.price_value is not None]
        if len(prices) > 1:
            import statistics
            mean_price = statistics.mean(prices)
            if mean_price > 0:
                cv = statistics.stdev(prices) / mean_price
                diversity_score = min(1.0, cv)  # Higher diversity = more activity
                factors.append(diversity_score)
        
        # Factor 3: Online seller ratio
        online_count = len([l for l in listings if getattr(l, 'account_online', True)])
        online_ratio = online_count / len(listings) if listings else 0
        factors.append(online_ratio)
        
        return sum(factors) / len(factors) if factors else 0.0
    
    def _create_empty_snapshot(self, item_type: str, league: str) -> MarketSnapshot:
        """Create an empty market snapshot when no data is available."""
        return MarketSnapshot(
            timestamp=datetime.now(),
            item_type=item_type,
            league=league,
            total_listings=0,
            active_listings=0,
            data_quality_score=0.0,
            sample_size=0,
            confidence_score=0.0,
            market_activity_score=0.0
        )
    
    def get_historical_data(
        self,
        item_type: str,
        league: str,
        days_back: int = 30
    ) -> HistoricalMarketData:
        """Get historical market data for trend analysis."""
        # Try cache first
        cache_key = f"historical:{item_type}:{league}:{days_back}"
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        # Get from database
        historical_data = self.db.get_historical_market_data(
            item_type=item_type,
            league=league,
            days_back=days_back
        )
        
        # Cache for 30 minutes
        self.cache.set(cache_key, historical_data, ttl_seconds=1800)
        
        return historical_data
    
    def analyze_trends(
        self,
        item_type: str,
        league: str,
        days_back: int = 30
    ) -> Optional[Any]:  # TrendAnalysis type
        """Analyze trends for an item."""
        # Check cache first
        cached_analysis = self.cache.get_cached_trend_analysis(item_type, league)
        if cached_analysis:
            return cached_analysis
        
        # Get historical data
        historical_data = self.get_historical_data(item_type, league, days_back)
        
        if not historical_data.snapshots:
            logger.warning(f"No historical data available for {item_type} in {league}")
            return None
        
        # Perform trend analysis
        analysis = self.trend_analyzer.analyze_comprehensive_trends(historical_data)
        
        # Cache the analysis
        self.cache.cache_trend_analysis(analysis)
        
        return analysis
    
    def get_trending_items(
        self,
        league: str,
        min_change_percent: float = 10.0,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get items with significant price trends."""
        return self.db.get_trending_items(
            league=league,
            min_change_percent=min_change_percent,
            limit=limit
        )
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """Clean up old historical data."""
        self.db.cleanup_old_data(days_to_keep)
        self.cache.clear()  # Clear cache after cleanup
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage system statistics."""
        db_stats = self.db.get_database_stats()
        cache_stats = self.cache.get_stats()
        
        return {
            'database': db_stats,
            'cache': cache_stats,
            'storage_system': {
                'total_snapshots': db_stats.get('market_snapshots_count', 0),
                'total_modifier_trends': db_stats.get('modifier_trends_count', 0),
                'total_price_trends': db_stats.get('price_trends_count', 0),
                'cache_hit_rate': cache_stats.get('hit_rate_percent', 0),
                'database_size_mb': db_stats.get('database_size_mb', 0)
            }
        }
