{% extends "base.html" %}

{% block title %}PoE 2 Trade Valuation - Dashboard{% endblock %}

{% block content %}
<div class="row">
    <!-- Welcome Section -->
    <div class="col-12 mb-4">
        <div class="card glass-effect">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="bi bi-gem text-gold"></i>
                    PoE 2 Precursor Tablet Valuation Tool
                </h4>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <p class="mb-2">
                            <strong>Analyze precursor tablet modifier values</strong> using real-time market data from the Path of Exile 2 trade API.
                            Get instant valuations for every modifier and discover profitable synergies.
                        </p>
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success"></i> Real-time modifier pricing</li>
                            <li><i class="bi bi-check-circle text-success"></i> Synergy combination detection</li>
                            <li><i class="bi bi-check-circle text-success"></i> Confidence scoring for reliability</li>
                        </ul>
                    </div>
                    <div class="col-md-4 text-center">
                        <a href="/precursor-analysis" class="btn btn-warning btn-lg">
                            <i class="bi bi-gem"></i> Start Analysis
                        </a>
                        <p class="text-muted mt-2 small">Analyze all modifiers instantly</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Search Section -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-search"></i> Item Search
                </h5>
            </div>
            <div class="card-body">
                <form id="searchForm">
                    <div class="mb-3">
                        <label for="searchLeague" class="form-label">League</label>
                        <select class="form-select" id="searchLeague">
                            {% for league in leagues %}
                            <option value="{{ league }}" {% if league == default_league %}selected{% endif %}>
                                {{ league }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="itemType" class="form-label">Item Type</label>
                        <input type="text" class="form-control" id="itemType" 
                               placeholder="e.g., Precursor Tablet" value="Precursor Tablet">
                        <div class="form-text">
                            Try: "Precursor Tablet", "Breach Precursor Tablet", "Delirium Precursor Tablet"
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maxResults" class="form-label">Max Results</label>
                        <select class="form-select" id="maxResults">
                            <option value="25">25 items</option>
                            <option value="50" selected>50 items</option>
                            <option value="100">100 items</option>
                            <option value="200">200 items</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="searchBtn">
                        <i class="bi bi-search"></i> Search Items
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Analysis Section -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-graph-up"></i> Market Analysis
                </h5>
            </div>
            <div class="card-body">
                <form id="analysisForm">
                    <div class="mb-3">
                        <label for="analysisLeague" class="form-label">League</label>
                        <select class="form-select" id="analysisLeague">
                            {% for league in leagues %}
                            <option value="{{ league }}" {% if league == default_league %}selected{% endif %}>
                                {{ league }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="analysisItemType" class="form-label">Item Type</label>
                        <input type="text" class="form-control" id="analysisItemType" 
                               placeholder="e.g., Precursor Tablet" value="Precursor Tablet">
                    </div>
                    
                    <div class="mb-3">
                        <label for="analysisMaxResults" class="form-label">Sample Size</label>
                        <select class="form-select" id="analysisMaxResults">
                            <option value="100" selected>100 items</option>
                            <option value="200">200 items</option>
                            <option value="300">300 items</option>
                            <option value="500">500 items</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-success w-100" id="analysisBtn">
                        <i class="bi bi-graph-up"></i> Analyze Market
                    </button>
                </form>
                
                <hr>
                
                <button type="button" class="btn btn-warning w-100" id="precursorBtn">
                    <i class="bi bi-gem"></i> Analyze All Precursor Tablets
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Results Section -->
<div class="row">
    <div class="col-12">
        <div id="resultsSection" style="display: none;">
            <!-- Search Results -->
            <div id="searchResults" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul"></i> Search Results
                            <span id="searchResultsCount" class="badge bg-primary ms-2"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-dark table-striped">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Rarity</th>
                                        <th>Level</th>
                                        <th>Price</th>
                                        <th>Seller</th>
                                        <th>Modifiers</th>
                                    </tr>
                                </thead>
                                <tbody id="searchResultsTable">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analysis Results -->
            <div id="analysisResults" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up"></i> Market Analysis Results
                        </h5>
                    </div>
                    <div class="card-body" id="analysisResultsContent">
                        <!-- Analysis results will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Precursor Analysis Results -->
            <div id="precursorResults" style="display: none;">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-gem"></i> Precursor Tablet Analysis
                        </h5>
                    </div>
                    <div class="card-body" id="precursorResultsContent">
                        <!-- Precursor analysis results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Search form handler
    document.getElementById('searchForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const league = document.getElementById('searchLeague').value;
        const itemType = document.getElementById('itemType').value;
        const maxResults = document.getElementById('maxResults').value;
        
        if (!itemType.trim()) {
            showToast('Please enter an item type', 'error');
            return;
        }
        
        setLoading('searchBtn', true);
        
        try {
            const response = await fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    league: league,
                    item_type: itemType,
                    max_results: parseInt(maxResults)
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displaySearchResults(data.results, data.search_params);
                showToast(`Found ${data.count} items`, 'success');
            } else {
                showToast(data.error, 'error');
            }
        } catch (error) {
            showToast('Search failed: ' + error.message, 'error');
        } finally {
            document.getElementById('searchBtn').innerHTML = '<i class="bi bi-search"></i> Search Items';
            document.getElementById('searchBtn').disabled = false;
        }
    });
    
    // Analysis form handler
    document.getElementById('analysisForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const league = document.getElementById('analysisLeague').value;
        const itemType = document.getElementById('analysisItemType').value;
        const maxResults = document.getElementById('analysisMaxResults').value;
        
        if (!itemType.trim()) {
            showToast('Please enter an item type', 'error');
            return;
        }
        
        setLoading('analysisBtn', true);
        
        try {
            const response = await fetch('/api/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    league: league,
                    item_type: itemType,
                    max_results: parseInt(maxResults)
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displayAnalysisResults(data.analysis, data.raw_data);
                showToast('Analysis completed successfully', 'success');
            } else {
                showToast(data.error, 'error');
            }
        } catch (error) {
            showToast('Analysis failed: ' + error.message, 'error');
        } finally {
            document.getElementById('analysisBtn').innerHTML = '<i class="bi bi-graph-up"></i> Analyze Market';
            document.getElementById('analysisBtn').disabled = false;
        }
    });
    
    // Precursor analysis handler
    document.getElementById('precursorBtn').addEventListener('click', async function() {
        const league = document.getElementById('analysisLeague').value;
        
        setLoading('precursorBtn', true);
        
        try {
            const response = await fetch('/api/precursor-analysis', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    league: league,
                    max_results: 200
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displayPrecursorResults(data.tablet_analysis, data.summary);
                showToast('Precursor analysis completed', 'success');
            } else {
                showToast(data.error, 'error');
            }
        } catch (error) {
            showToast('Precursor analysis failed: ' + error.message, 'error');
        } finally {
            document.getElementById('precursorBtn').innerHTML = '<i class="bi bi-gem"></i> Analyze All Precursor Tablets';
            document.getElementById('precursorBtn').disabled = false;
        }
    });
    
    // Display search results
    function displaySearchResults(results, searchParams) {
        const tableBody = document.getElementById('searchResultsTable');
        const countBadge = document.getElementById('searchResultsCount');
        
        countBadge.textContent = results.length;
        
        tableBody.innerHTML = '';
        
        results.forEach(result => {
            const row = document.createElement('tr');
            row.className = 'fade-in';
            
            const modifiersHtml = result.item.modifiers.slice(0, 3).map(mod => 
                `<small class="modifier-${mod.type}">${mod.text}</small>`
            ).join('<br>');
            
            const moreModsText = result.item.modifiers.length > 3 ? 
                `<br><small class="text-muted">+${result.item.modifiers.length - 3} more</small>` : '';
            
            row.innerHTML = `
                <td>
                    <strong class="${getRarityClass(result.item.rarity)}">
                        ${result.item.name || result.item.type_line}
                    </strong>
                    <br><small class="text-muted">${result.item.type_line}</small>
                </td>
                <td><span class="${getRarityClass(result.item.rarity)}">${result.item.rarity}</span></td>
                <td>${result.item.item_level}</td>
                <td>${result.price ? formatPrice(result.price.amount, result.price.currency) : 'No price'}</td>
                <td>
                    ${result.seller.name}
                    ${result.seller.online ? '<i class="bi bi-circle-fill text-success" title="Online"></i>' : '<i class="bi bi-circle text-muted" title="Offline"></i>'}
                </td>
                <td>${modifiersHtml}${moreModsText}</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('searchResults').style.display = 'block';
        document.getElementById('analysisResults').style.display = 'none';
        document.getElementById('precursorResults').style.display = 'none';
    }
    
    // Display analysis results
    function displayAnalysisResults(analysis, rawData) {
        const content = document.getElementById('analysisResultsContent');
        
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-gold">Market Summary</h6>
                    <ul class="list-unstyled">
                        <li><strong>Item Type:</strong> ${analysis.item_type}</li>
                        <li><strong>League:</strong> ${analysis.league}</li>
                        <li><strong>Total Listings:</strong> ${analysis.total_listings}</li>
                        <li><strong>Data Quality:</strong> 
                            <span class="badge ${rawData.data_quality > 0.8 ? 'bg-success' : rawData.data_quality > 0.5 ? 'bg-warning' : 'bg-danger'}">
                                ${analysis.summary.data_quality_rating}
                            </span>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6 class="text-gold">Base Price</h6>
                    ${rawData.base_price ? `
                        <ul class="list-unstyled">
                            <li><strong>Median Price:</strong> ${formatPrice(rawData.base_price.amount, rawData.base_price.currency)}</li>
                            <li><strong>Confidence:</strong> ${(rawData.base_price.confidence * 100).toFixed(1)}%</li>
                        </ul>
                    ` : '<p class="text-muted">No base price data available</p>'}
                </div>
            </div>
        `;
        
        if (analysis.valuable_modifiers && analysis.valuable_modifiers.length > 0) {
            html += `
                <hr>
                <h6 class="text-gold">Valuable Modifiers</h6>
                <div class="table-responsive">
                    <table class="table table-dark table-sm">
                        <thead>
                            <tr>
                                <th>Modifier</th>
                                <th>Value</th>
                                <th>Confidence</th>
                                <th>Listings</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            analysis.valuable_modifiers.forEach(mod => {
                html += `
                    <tr>
                        <td><small>${mod.text}</small></td>
                        <td>${formatPrice(mod.value, mod.currency)}</td>
                        <td>
                            <span class="badge ${mod.confidence > 0.8 ? 'bg-success' : mod.confidence > 0.5 ? 'bg-warning' : 'bg-danger'}">
                                ${(mod.confidence * 100).toFixed(1)}%
                            </span>
                        </td>
                        <td>${mod.listings}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table></div>';
        }
        
        content.innerHTML = html;
        
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('searchResults').style.display = 'none';
        document.getElementById('analysisResults').style.display = 'block';
        document.getElementById('precursorResults').style.display = 'none';
    }
    
    // Display precursor analysis results
    function displayPrecursorResults(tabletAnalysis, summary) {
        const content = document.getElementById('precursorResultsContent');
        
        let html = `
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> Analysis Summary</h6>
                <p class="mb-0">
                    Analyzed <strong>${summary.total_tablet_types}</strong> different precursor tablet types in <strong>${summary.league}</strong>.
                    Sample size: up to ${summary.max_results_per_type} items per type.
                </p>
            </div>
        `;
        
        Object.entries(tabletAnalysis).forEach(([tabletType, analysis]) => {
            html += `
                <div class="card mb-3 glass-effect">
                    <div class="card-header">
                        <h6 class="mb-0 text-gold">${tabletType}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <p><strong>Total Listings:</strong> ${analysis.total_listings}</p>
                                <p><strong>Data Quality:</strong> 
                                    <span class="badge ${analysis.summary.data_quality_rating === 'High' ? 'bg-success' : analysis.summary.data_quality_rating === 'Medium' ? 'bg-warning' : 'bg-danger'}">
                                        ${analysis.summary.data_quality_rating}
                                    </span>
                                </p>
                                <p><strong>High Confidence Modifiers:</strong> ${analysis.summary.high_confidence_modifiers}</p>
                            </div>
                            <div class="col-md-4">
                                ${analysis.valuable_modifiers && analysis.valuable_modifiers.length > 0 ? `
                                    <h6 class="text-gold">Top Modifier</h6>
                                    <p><small>${analysis.valuable_modifiers[0].text}</small></p>
                                    <p><strong>${formatPrice(analysis.valuable_modifiers[0].value, analysis.valuable_modifiers[0].currency)}</strong></p>
                                ` : '<p class="text-muted">No valuable modifiers found</p>'}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        content.innerHTML = html;
        
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('searchResults').style.display = 'none';
        document.getElementById('analysisResults').style.display = 'none';
        document.getElementById('precursorResults').style.display = 'block';
    }
</script>
{% endblock %}
